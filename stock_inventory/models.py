from datetime import datetime
from typing import Optional

from django.conf import settings
from django.core.validators import MinValueValidator
from django.db import models, transaction
from django.db.models.query import QuerySet
from django.db.models.functions import ExtractMonth
import openpyxl
import pandas as pd
import pytz
from rest_framework import serializers

from core.models import BaseModel, DeleteHandler
from helpers.custom_filters import QuerysetCustomFilter
from helpers.enums import (
    Backorders,
    Currency,
    AlignmentTypes,
    InterfaceTheme,
    RequestMethodType,
)
from helpers.reusable_functions import strip_all_whitespaces
from requisition.models import Company
from stock_inventory.helper import enums
from stock_inventory.utils import merge_stock_sheet


User = settings.AUTH_USER_MODEL
logger = settings.LOGGER


# Create your model(s) here.
class Branch(BaseModel):
    company = models.ForeignKey(
        Company, on_delete=models.CASCADE, related_name="company_branch"
    )
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    phone = models.CharField(max_length=25, null=True, blank=True)
    address = models.TextField()
    sell_without_inventory = models.BooleanField(default=False)
    transfer_charges_to_customer = models.BooleanField(default=False)
    use_product_vat = models.BooleanField(default=False)
    allow_store_credit = models.BooleanField(default=False)
    vat = models.FloatField(default=0.0, validators=[MinValueValidator(0.0)])
    is_super_branch = models.BooleanField(default=False)
    pos_device = models.ManyToManyField(
        "sales_app.POSDevice",
        blank=True,
    )
    is_pos_default = models.BooleanField(default=False)
    updated_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="updated_branch",
    )

    def __str__(self) -> str:
        return f"{self.company.company_name} - {self.name}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "BRANCH"
        verbose_name_plural = "BRANCHES"
        constraints = [
            models.UniqueConstraint(
                fields=["company", "name"],
                name="unique_company_branch",
            )
        ]

    def save(self, *args, **kwargs) -> None:
        self.name = self.name.title()
        super(Branch, self).save(*args, **kwargs)

    @classmethod
    def create_company_branch(
        cls,
        user: User,
        company: Company,
        name: str,
        address: str,
        vat: float,
        is_super_branch: Optional[bool] = False,
    ):
        """
        This method creates a company branch.
        Args:
            user (User): The user creating the branch.
            company (Company): The parent company to which the branch belongs.
            name (str): The location or name of the branch.
            address (str): The address of the branch.
            vat (float): The VAT (Value Added Tax) associated with the branch.
            is_super_branch (bool, optional): Indicates if the branch is a super branch. Default is False.
        Returns:
            Branch or bool: The created branch instance if successful, or False if the branch already exists.
        NOTE:
        - from inception virtual account(s) were only created for Branches whose parent company owns a
            corporate wallet. However, recent requirements made provision for virtual account(s) for all
            branches regardless of their company's status.
        - branch vat should be made available as a Tax object.
        """
        from account.tasks import create_wema_sales_account
        from invoicing.models import Tax

        check_branch = cls.objects.filter(
            company=company,
            name=name.title(),
        )
        if check_branch.exists():
            return False
        branch = cls.objects.create(
            company=company,
            name=name,
            address=address,
            vat=vat,
            is_super_branch=is_super_branch,
            created_by=user,
        )
        Tax.add(
            company=company,
            branch=branch,
            title="Value Added Tax",
            rate=vat,
            created_by=user,
        )
        create_wema_sales_account.delay(branch.id)
        return branch

    @classmethod
    def retrieve_company_branch(cls, id: str, company: Company):
        """
        Retrieve a Company's Branch by its ID and parent company.
        Returns:
            Branch or None: The retrieved Branch instance if found, or None.
        """
        try:
            branch = cls.objects.get(id=id, company=company)
        except cls.DoesNotExist:
            branch = None
        return branch

    @classmethod
    def destroy(cls, company: Company, branch: object):
        stock_details = StockDetail.objects.filter(company=company, branch=branch)
        if stock_details.exists():
            return {
                "status": False,
                "message": "can not delete a branch with active inventory.",
            }
        branch.delete()
        return {"status": True, "message": "branch deleted successfully."}


class Category(BaseModel, DeleteHandler):
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    name = models.CharField(max_length=255)
    has_products = models.BooleanField(default=False)
    updated_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="updated_category",
    )

    def __str__(self) -> str:
        return f"{self.company} => {self.name}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CATEGORY"
        verbose_name_plural = "CATEGORIES"

    def save(self, *args, **kwargs) -> None:
        self.name = self.name.title()
        super(Category, self).save(*args, **kwargs)

    @classmethod
    def get_general(cls):
        try:
            general_category = cls.objects.get(name="General")
        except cls.DoesNotExist:
            general_category = cls.objects.create(name="General")
        return general_category

    @classmethod
    def retrieve_create_category_name(
        cls, name: str, company: Optional[Company] = None, user: Optional[User] = None
    ):
        """ """
        if company is not None:
            category = cls.objects.filter(
                company=company,
                name=name.title(),
                is_active=True,
            ).first()
        else:
            category = cls.objects.filter(
                company__isnull=True,
                name=name.title(),
                is_active=True,
            ).first()
        if category is None:
            category = cls.objects.create(name=name, company=company, created_by=user)
        return category

    @classmethod
    def create_company_categories(cls, user: User, company: Company, categories: list):
        """
        Create company categories, handling duplicates gracefully.
        Args:
            user (User): The user creating the categories.
            company (Company): The company for which categories are being created.
            categories (list): A list of dictionaries containing category data.
        Returns:
            Categories: The newly created category object(s).
        """
        company_categories = []
        for data in categories:
            name = data.get("name")
            category = cls.retrieve_create_category_name(
                name=name,
                company=company,
                user=user,
            )
            company_categories.append(category)
        return company_categories


class SubCategory(BaseModel, DeleteHandler):
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    def __str__(self) -> str:
        return f"{self.category.__str__()} => {self.name}"

    def save(self, *args, **kwargs) -> None:
        self.name = self.name.title()
        super(SubCategory, self).save(*args, **kwargs)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "SUB CATEGORY"
        verbose_name_plural = "SUB CATEGORIES"


class Product(BaseModel, DeleteHandler):
    """
    UNLISTING:
    - unlist is used to hide a product from a branch's display of items.
    - unlist is achieved by removing the branch from the selected branches.
    """

    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    subcategory = models.ForeignKey(
        SubCategory,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    name = models.CharField(max_length=255)
    product_id = models.CharField(max_length=255, null=True, blank=True)
    spec_model_variant = models.CharField(max_length=255, null=True, blank=True)
    product_price = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )
    selling_price = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )
    vat = models.FloatField(default=0.0, validators=[MinValueValidator(0.0)])
    discount = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )
    selected_branches = models.ManyToManyField(Branch, blank=True)
    product_description = models.TextField(null=True, blank=True)
    product_tag = models.CharField(max_length=255, null=True, blank=True)
    sku = models.CharField(max_length=255, null=True, blank=True)
    sell_without_inventory = models.BooleanField(default=False)
    is_restaurant_item = models.BooleanField(default=False)
    product_location = models.CharField(max_length=255, null=True, blank=True)
    product_image_1 = models.URLField(max_length=2300, null=True, blank=True)
    product_image_2 = models.URLField(max_length=2300, null=True, blank=True)
    product_image_3 = models.URLField(max_length=2300, null=True, blank=True)
    product_image_4 = models.URLField(max_length=2300, null=True, blank=True)
    product_image_5 = models.URLField(max_length=2300, null=True, blank=True)
    currency = models.CharField(
        max_length=3,
        choices=Currency.choices,
        default=Currency.NAIRA,
    )
    has_variants = models.BooleanField(default=False)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    updated_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="updated_product",
    )
    source = models.CharField(
        max_length=100,
        choices=enums.ProductSource.choices,
        null=True,
        blank=True,
    )
    sales_ranking = models.PositiveIntegerField(default=0)

    def __str__(self) -> str:
        return self.name

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "PRODUCT"
        verbose_name_plural = "PRODUCTS"
        constraints = [
            models.UniqueConstraint(
                fields=["company", "sku"],
                name="unique_company_product_sku",
            )
        ]

    def save(self, *args, **kwargs) -> None:
        self.name = self.name.title()
        super(Product, self).save(*args, **kwargs)

    @classmethod
    def create_company_product(cls, user: User, company: Company, products: list):
        """
        Create or retrieve products for a company based on the provided data.
        Args:
            user (User): The user creating the products.
            company (Company): The company for which products are being created.
            products (list): A list of dictionaries containing product data.
        Returns:
            list: A list of dictionaries containing the id and name of each product retrieved or created.
        """
        product_list = list()

        for data in products:
            category = data.get("category")
            names = data.get("name")
            stocks = data.get("stocks")
            selected_branches = data.get("selected_branches")
            # product_data = dict()

            if names:
                for name in names:
                    product = cls.retrieve_create_product_name(
                        company=company,
                        category=category,
                        name=name,
                        user=user,
                    )
                    product_data = {
                        "id": product.id,
                        "name": str(product.name).capitalize(),
                    }
                    product_list.append(product_data)
            else:
                for stock in stocks:
                    product = cls.retrieve_create_product_name(
                        company=company,
                        category=category,
                        name=stock.get("item"),
                        user=user,
                    )
                    product.sku = (
                        stock.get("sku") if stock.get("sku") != None else product.sku
                    )
                    product.product_image_1 = stock.get("image")
                    if selected_branches is not None:
                        product.selected_branches.set(selected_branches)
                    if stock.get("has_stock"):
                        # Product attr(s).
                        product.product_price = stock.get("stock_price")
                        product.selling_price = stock.get("selling_price")
                        if selected_branches:
                            branch = selected_branches[0]
                        else:
                            branch = Branch.objects.filter(
                                company=company,
                                is_super_branch=True,
                            ).last()
                        if branch is None:
                            branch = Branch.objects.filter(company=company).last()
                        # Add stock details to the inventory.
                        StockDetail.add_stock(
                            company=company,
                            branch=branch,
                            user=user,
                            category=category,
                            product=product,
                            quantity=stock.get("quantity"),
                            cost_price=stock.get("stock_price"),
                            selling_price=stock.get("selling_price"),
                            image=stock.get("image"),
                            sku=stock.get("sku"),
                        )
                    product.save()
                    product_data = {
                        "id": product.id,
                        "name": str(product.name).capitalize(),
                    }
                    product_list.append(product_data)

        return product_list

    @classmethod
    def retrieve_product(cls, id: str, company: Company):
        """
        Returns:
            Product or None: The retrieved product instance if found, or None if it doesn't exist.
        """
        try:
            product = cls.objects.get(
                id=id,
                company=company,
                is_active=True,
            )
        except cls.DoesNotExist:
            product = None
        return product

    @classmethod
    def retrieve_create_product_name(
        cls,
        name: str,
        company: Company,
        category: Category,
        user: Optional[User],
    ):
        """
        Returns:
            Product: The retrieved or newly created product object.
        """
        product = cls.objects.filter(
            company=company,
            name=name.title(),
            is_active=True,
        ).last()
        if product is None:
            product = cls.objects.create(
                name=name,
                company=company,
                category=category,
                created_by=user,
            )
            category.has_products = True
            category.save()
        return product

    @classmethod
    def retrieve_create_product_name_pos(
        cls,
        name: str,
        company: Company,
        category: Category,
        product_price: float,
        selling_price: float,
        user: Optional[User],
    ):
        """
        Returns:
            Product: The retrieved or newly created product object.
        """
        product = cls.objects.filter(
            company=company,
            name=name.title(),
        ).last()
        if product is None:
            product = cls.objects.create(
                name=name,
                company=company,
                category=category,
                created_by=user,
                product_price=product_price,
                selling_price=selling_price,
            )
            category.has_products = True
            category.save()
        return product

    @classmethod
    def create_product_and_variant(
        cls,
        user: User,
        company: Company,
        branch: Branch,
        category: Category,
        product_name: str,
        product_description: str,
        product_tag: str,
        stock_quantity: int,
        cost_price: float,
        selling_price: float,
        discount: float,
        stock_alert: Optional[int] = None,
        has_variants: Optional[bool] = False,
        variants: Optional[list] = None,
        restaurant_item: Optional[bool] = False,
        sell_without_inventory: Optional[bool] = False,
        currency: Optional[str] = Currency.NAIRA,
        product_image_1: Optional[str] = None,
        product_image_2: Optional[str] = None,
        product_image_3: Optional[str] = None,
        product_image_4: Optional[str] = None,
        product_image_5: Optional[str] = None,
        product_location: Optional[str] = None,
        allow_backorders: Optional[str] = "DO_NOT_ALLOW",
        low_stock_threshold: Optional[int] = 0,
        out_of_stock_threshold: Optional[int] = 0,
        item_sold_individually: Optional[bool] = False,
    ):
        """ """
        with transaction.atomic():
            product_ins = cls.objects.create(
                created_by=user,
                company=company,
                category=category,
                name=product_name,
                product_description=product_description,
                product_tag=product_tag,
                product_location=product_location,
                product_price=cost_price,
                selling_price=selling_price,
                has_variants=has_variants,
                is_restaurant_item=restaurant_item,
                sell_without_inventory=sell_without_inventory,
                currency=currency,
                discount=discount,
                product_image_1=product_image_1,
                product_image_2=product_image_2,
                product_image_3=product_image_3,
                product_image_4=product_image_4,
                product_image_5=product_image_5,
            )
            product_ins.selected_branches.add(branch)
            StockDetail.add_stock(
                company=company,
                branch=branch,
                user=user,
                category=category,
                product=product_ins,
                description=product_description,
                quantity=stock_quantity,
                cost_price=cost_price,
                selling_price=selling_price,
                stock_alert=stock_alert,
                has_variants=has_variants,
                allow_backorders=allow_backorders,
                low_stock_threshold=low_stock_threshold,
                out_of_stock_threshold=out_of_stock_threshold,
                item_sold_individually=item_sold_individually,
                image=product_image_1,
            )
            if has_variants:
                for variant in variants:
                    description = variant.get("description", None)
                    product_value = variant.get("product_value", None)
                    quantity = variant.get("quantity", None)
                    variant_price = variant.get("variant_price", None)
                    selling_price = variant.get("selling_price", None)
                    variant_discount = variant.get("variant_discount", None)
                    currency = variant.get("currency", None)

                    product_ins = cls.objects.create(
                        created_by=user,
                        company=company,
                        category=category,
                        name=product_name,
                        product_description=f"{product_description} {description} {product_value}",
                        product_tag=product_tag,
                        product_price=variant_price,
                        selling_price=selling_price,
                        has_variants=has_variants,
                        is_restaurant_item=restaurant_item,
                        sell_without_inventory=sell_without_inventory,
                        currency=currency,
                        discount=variant_discount,
                        product_image_1=product_image_1,
                        product_image_2=product_image_2,
                        product_image_3=product_image_3,
                        product_image_4=product_image_4,
                        product_image_5=product_image_5,
                    )
                    product_ins.selected_branches.add(branch)
                    StockDetail.add_stock(
                        company=company,
                        branch=branch,
                        user=user,
                        category=category,
                        product=product_ins,
                        quantity=quantity,
                        description=f"{product_description} {description} {product_value}",
                        cost_price=variant_price,
                        stock_alert=stock_alert,
                        selling_price=selling_price,
                        has_variants=has_variants,
                        image=product_image_1,
                    )
        return True


class Supplier(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    name = models.CharField(max_length=225)
    email = models.EmailField(null=True, blank=True)
    phone_number = models.CharField(max_length=25, null=True, blank=True)
    address = models.TextField(null=True, blank=True)
    bank_name = models.CharField(max_length=255, null=True, blank=True)
    bank_code = models.CharField(max_length=255, null=True, blank=True)
    bank_account_name = models.CharField(max_length=255, null=True, blank=True)
    bank_account_number = models.CharField(max_length=25, null=True, blank=True)
    landmark = models.CharField(max_length=150, null=True, blank=True)
    lga = models.CharField(max_length=150, null=True, blank=True)
    city = models.CharField(max_length=150, null=True, blank=True)
    state = models.CharField(max_length=150, null=True, blank=True)
    country = models.CharField(max_length=150, null=True, blank=True)
    logo = models.ImageField(upload_to="suppliers-logo", blank=True, null=True)
    contact_person_name = models.CharField(max_length=200, null=True, blank=True)
    contact_person_email = models.EmailField(null=True, blank=True)
    contact_person_phone_no = models.CharField(max_length=25, null=True, blank=True)
    contact_person_position = models.CharField(max_length=100, null=True, blank=True)
    registration_no = models.CharField(max_length=200, null=True, blank=True)
    registration_doc = models.FileField(
        upload_to="supplier-documents", blank=True, null=True
    )
    category = models.ManyToManyField(Category, blank=True)
    otp = models.TextField(blank=True, null=True)
    code_expiry = models.DateTimeField(blank=True, null=True)
    is_active = models.BooleanField(default=False)
    is_approved = models.BooleanField(default=False)
    approved_at = models.DateTimeField(blank=True, null=True)
    updated_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="updated_supplier",
    )

    def __str__(self) -> str:
        return self.name

    def save(self, *args, **kwargs) -> None:
        self.name = self.name.title()
        super(Supplier, self).save(*args, **kwargs)

    def get_full_address(self):
        address = self.address
        if self.landmark:
            address += f", {self.landmark}"
        if self.lga:
            address += f", {self.lga}"
        if self.city:
            address += f", {self.city}"
        if self.state:
            address += f", {self.state}"
        if self.country:
            address += f", {self.country}"
        return address

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "SUPPLIER / VENDOR"
        verbose_name_plural = "SUPPLIERS / VENDORS"

    @classmethod
    def destroy(cls, id: str, company: Company):
        """
        This method performs a soft delete on the identified supplier.
        Args:
            id (str): The unique identifier of the supplier to be deleted.
            company (Company): The Company object to whom the supplier belongs.
        Returns:
        dict or None:
        - a dictionary containing a success message
        - None if the supplier does not exist
        """
        supplier = cls.objects.filter(id=id, company=company).first()
        if supplier is not None:
            supplier.delete()
            return {"message": "supplier deleted successfully."}
        return None

    @classmethod
    def retrieve_supplier_name(cls, name: str, company: Company, user: User):
        if name is None:
            return None
        try:
            supplier = cls.objects.get(name=name.title(), company=company)
        except cls.DoesNotExist:
            supplier = cls.objects.create(
                name=name,
                company=company,
                created_by=user,
            )
        return supplier


class SupplierHistory(BaseModel):
    """
    A class representing a supply manager.
    This class provides methods for registering supply information.
    """

    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE)
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    subcategory = models.ForeignKey(
        SubCategory,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    item = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=0)
    stock_price = models.DecimalField(
        max_digits=13, decimal_places=2, validators=[MinValueValidator(0.00)]
    )
    stock_value = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        editable=False,
        validators=[MinValueValidator(0.00)],
    )
    returned_items = models.PositiveIntegerField(default=0)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)

    def __str__(self) -> str:
        return self.supplier.name

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "SUPPLIER HISTORY"
        verbose_name_plural = "SUPPLIER HISTORIES"

    def save(self, *args, **kwargs) -> None:
        self.stock_value = float((self.quantity * self.stock_price))
        super(SupplierHistory, self).save(*args, **kwargs)

    @classmethod
    def rating(cls, total_supplied: int, total_returned: int):
        """
        This method calculates a rating by measuring the percentage of returned items
        in comparison to the total supply (the higher the percentage of returned items,
        the lower the rating will be).
        Args:
            total_supply (int): The total number of items supplied.
            total_returned (int): The total number of items that were returned.
        Returns:
            float: The calculated rating as a percentage.
        """
        return 100 - ((total_returned / total_supplied) * 100)

    @classmethod
    def register_supply(
        cls,
        company: Company,
        supplied_by: Supplier,
        category: Category,
        item: Product,
        quantity: int,
        stock_price: float,
    ):
        """
        Register a new supply entry.
        This method creates and saves a new supply entry with the provided information.
        Args:
            user: The user associated with the supply.
            company: The company associated with the supply.
            supplier: The supplier associated with the supply.
            category: The category of the supply.
            item: The item of the supply.
            quantity: The quantity of the supply.
            stock_price: The stock price of the supply.
        Returns:
            supply: The newly created supply object.
        """
        supply = cls.objects.create(
            company=company,
            supplier=supplied_by,
            category=category,
            item=item,
            quantity=quantity,
            stock_price=stock_price,
        )
        return supply

    from django.db.models import Sum

    @classmethod
    def suppliers_performance_rating(cls, company: Company):
        """
        Calculate supplier performances based on supply history.
        This method calculates and ranks supplier performances for a given company.
        It retrieves supply history data, aggregates quantities, and calculates ratings for each supplier.
        The results are sorted by rating in descending order.
        Args:
            company (Company): The company for which supplier performances are calculated.
        Returns:
            list[dict] or None: A list of dictionaries containing supplier performance data.
                Each dictionary includes "supplier_name", "products_supplied", and "rating" keys.
                If no supply history exists, returns None.
        """
        supplier_performances = {}

        company_supply_history = cls.objects.filter(company=company).select_related(
            "supplier"
        )

        if company_supply_history.exists():
            supplier_history = company_supply_history.values("supplier").annotate(
                total_supplied=models.Sum("quantity"),
                total_returned=models.Sum("returned_items"),
            )
            for supplier_data in supplier_history:
                supplier_id = supplier_data["supplier"]
                supplier_instance = Supplier.objects.get(pk=supplier_id)
                total_supplied = supplier_data["total_supplied"] or 0
                total_returned = supplier_data["total_returned"] or 0
                rating = cls.rating(
                    total_supplied=total_supplied, total_returned=total_returned
                )
                supplier_performances[supplier_instance.name] = {
                    "supplier_name": supplier_instance.name,
                    "products_supplied": total_supplied,
                    "rating": rating,
                }

            sorted_performances = sorted(
                supplier_performances.values(), key=lambda x: x["rating"], reverse=True
            )
            return sorted_performances
        return None


class StockDetail(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE)
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    subcategory = models.ForeignKey(
        SubCategory,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    item = models.ForeignKey(Product, on_delete=models.CASCADE)
    vat = models.FloatField(default=0.0, validators=[MinValueValidator(0.0)])
    quantity = models.IntegerField(default=0)
    stock_price = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )
    selling_price = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )
    stock_value = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        editable=False,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )
    description = models.TextField(null=True, blank=True)
    name = models.CharField(max_length=150, null=True, blank=True)
    sku = models.CharField(max_length=150, null=True, blank=True)
    measure_unit = models.CharField(max_length=150, null=True, blank=True)
    bulk_name = models.CharField(max_length=150, null=True, blank=True)
    bulk_qty = models.PositiveIntegerField(default=0)
    spec_name_1 = models.CharField(max_length=150, null=True, blank=True)
    spec_value_1 = models.CharField(max_length=150, null=True, blank=True)
    spec_name_2 = models.CharField(max_length=150, null=True, blank=True)
    spec_value_2 = models.CharField(max_length=150, null=True, blank=True)
    spec_name_3 = models.CharField(max_length=150, null=True, blank=True)
    spec_value_3 = models.CharField(max_length=150, null=True, blank=True)
    stock_on_hold = models.PositiveIntegerField(default=0)
    expiry_date = models.DateField(null=True, blank=True)
    stock_alert = models.PositiveIntegerField(default=0)
    has_variants = models.BooleanField(default=False)
    has_unique_ids = models.BooleanField(default=False)
    discount = models.FloatField(default=1)
    currency = models.CharField(
        max_length=3, choices=Currency.choices, default=Currency.NAIRA
    )
    supplied_by = models.ForeignKey(
        Supplier,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="updated_stock",
    )
    barcode = models.TextField(null=True, blank=True)
    image = models.URLField(max_length=2300, null=True, blank=True)
    has_bulk = models.BooleanField(default=False)
    show_in_web_stores = models.BooleanField(default=True)
    allow_backorders = models.CharField(
        max_length=25, choices=Backorders.choices, default=Backorders.DO_NOT_ALLOW
    )
    low_stock_threshold = models.PositiveIntegerField(default=0)
    out_of_stock_threshold = models.PositiveIntegerField(default=0)
    item_sold_individually = models.BooleanField(default=True)

    def __str__(self) -> str:
        return self.item.__str__()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "STOCK DETAIL"
        verbose_name_plural = "STOCK DETAILS"
        constraints = [
            models.UniqueConstraint(
                fields=[
                    "company",
                    "branch",
                    "category",
                    "item",
                ],
                name="company_branch_product",
            )
        ]

    def save(self, *args, **kwargs) -> None:
        if self.stock_price != None and self.quantity != None:
            self.stock_value = float(self.stock_price) * self.quantity
        super(StockDetail, self).save(*args, **kwargs)

    def is_below_low_stock(self):
        """Check if stock is below low stock threshold."""
        return self.quantity <= self.low_stock_threshold

    def is_out_of_stock(self):
        """Check if stock is out of stock."""
        return self.quantity <= self.out_of_stock_threshold

    def can_backorder(self):
        """Check if backorders are allowed."""
        return self.allow_backorders != "DO_NOT_ALLOW"

    @property
    def expected_profit(self):
        price = float(self.selling_price)
        if price > 0:
            sales_value = price * self.quantity
            return sales_value - float(self.stock_value)
        return 0

    @property
    def discounted_selling_price(self):
        price = float(self.selling_price)
        if price > 0:
            return price - (price * self.discount)
        return 0

    @classmethod
    def retrieve_branch_item_stock(cls, branch: Branch, item: Product):
        """
        This method attempts to retrieve the stock information for a specified item.
        Args:
            branch (Branch): The branch where the stock information is to be retrieved.
            item (Product): The product or item for which the stock information is needed.
        Returns:
            StockDetail or None: The StockDetail object if found, or None if not found.
        """
        try:
            branch_stock = cls.objects.get(branch=branch, item=item)
        except cls.DoesNotExist:
            branch_stock = None
        return branch_stock

    @classmethod
    def add_stock(
        cls,
        company: Company,
        branch: Branch,
        user: User,
        category: Category,
        product: Product,
        quantity: int,
        cost_price: float,
        selling_price: float,
        stock_alert: Optional[int] = 0,
        supplied_by: Optional[Supplier] = None,
        image: Optional[str] = None,
        description: Optional[str] = None,
        has_variants: Optional[bool] = False,
        allow_backorders: Optional[str] = "DO_NOT_ALLOW",
        low_stock_threshold: Optional[int] = 0,
        out_of_stock_threshold: Optional[int] = 0,
        item_sold_individually: Optional[bool] = False,
        comment: Optional[str] = None,
        subcategory: Optional[SubCategory] = None,
        sku: Optional[str] = None,
    ):
        """
        NOTE [LOGIC]:
        - if stock record is available for an item; it will be updated.
        - if stock record does not exist for an item; it will be created.
        """
        from stock_inventory.utils import register_stock_history

        if supplied_by is not None:
            SupplierHistory.register_supply(
                company=company,
                supplied_by=supplied_by,
                category=category,
                item=product,
                quantity=quantity,
                stock_price=cost_price,
            )
        stock_detail = cls.objects.filter(
            company=company, branch=branch, category=category, item=product
        ).first()
        if stock_detail is not None:
            quantity_before = stock_detail.quantity
            stock_detail.quantity = models.F("quantity") + quantity
            stock_detail.stock_price = cost_price
            stock_detail.selling_price = selling_price
            stock_detail.image = image if image is not None else stock_detail.image
            stock_detail.updated_by = user
            stock_detail.subcategory = (
                subcategory if subcategory is not None else stock_detail.subcategory
            )
            stock_detail.sku = sku if sku is not None else stock_detail.sku
            stock_detail.save()
        else:
            quantity_before = 0
            stock_detail = cls.objects.create(
                uploaded_by=user,
                company=company,
                branch=branch,
                category=category,
                subcategory=subcategory,
                item=product,
                quantity=quantity,
                stock_price=cost_price,
                selling_price=selling_price,
                stock_alert=stock_alert,
                supplied_by=supplied_by,
                image=image,
                description=description,
                sku=sku,
                has_variants=has_variants,
                allow_backorders=allow_backorders,
                low_stock_threshold=low_stock_threshold,
                out_of_stock_threshold=out_of_stock_threshold,
                item_sold_individually=item_sold_individually,
            )
        register_stock_history(
            company=company,
            branch=branch,
            category=category,
            item=product,
            price=float(cost_price),
            quantity_before=quantity_before,
            quantity=quantity,
            quantity_after=quantity_before + quantity,
            transaction_type=enums.StockHistoryChoices.STOCK_IN,
            status=enums.StockHistoryStatusChoices.INCOMING,
            created_by=user,
            comment=comment,
        )
        # Create price list for an item if not exists, else update it.
        price_list = PriceList.objects.filter(
            company=company, category=category, item=product
        ).first()
        if price_list is not None:
            price_list.price = selling_price
            price_list.save()
        else:
            price_list = PriceList.objects.create(
                company=company,
                created_by=user,
                category=category,
                item=product,
                price=selling_price,
            )
            price_list.selected_branches.set([branch])
            price_list.save()
        return stock_detail

    @classmethod
    def upload_stock_and_variant(
        cls,
        user: User,
        company: Company,
        branch: Branch,
        category: Category,
        item: Product,
        supplied_by: Optional[Supplier] = None,
        quantity: Optional[int] = None,
        expiry_date: Optional[str] = None,
        stock_alert: Optional[int] = 0,
        stock_price: Optional[float] = None,
        selling_price: Optional[float] = None,
        has_variants: Optional[bool] = False,
        has_unique_ids: Optional[bool] = False,
        variants: Optional[list] = None,
        comment: Optional[str] = None,
    ):
        """
        This is a method for uploading stock and variants information.
        It handles updating existing stock details or creating new ones if they don't exist.
        Returns:
            dict: A dictionary containing the key item and value as the stock detail uploaded.
        NOTE [LOGIC]:
        - The stock history action occurs before upload of stock.
        - If the specified stock details already exist, the method updates the existing details with the provided values.
        - If the stock details do not exist, a new stock entry is created with the provided information.
        - If the product has variants, it uses the `StockVariant.upload_variant` method to handle variant details.
        """
        if supplied_by is not None:
            supply_history = SupplierHistory.register_supply(
                company=company,
                supplied_by=supplied_by,
                category=category,
                item=item,
                quantity=quantity,
                stock_price=stock_price,
            )
        else:
            supply_history = None
        new_quantity = quantity if quantity is not None else 0
        stock_detail = cls.add_stock(
            company=company,
            branch=branch,
            user=user,
            category=category,
            product=item,
            quantity=new_quantity,
            cost_price=stock_price,
            selling_price=selling_price,
            stock_alert=stock_alert,
            supplied_by=supplied_by,
            comment=comment,
        )
        if has_variants:
            StockVariant.upload_variant(
                user=user,
                company=company,
                branch=branch,
                stock_item=stock_detail,
                supply_history=supply_history,
                variants=variants,
            )
        return {"item": stock_detail}

    @classmethod
    def upload_stock_details(
        cls,
        user: User,
        company: Company,
        branch: Branch,
        stocks: list,
        comment: Optional[str] = None,
    ):
        """ """

        with transaction.atomic():
            for stock in stocks:
                supplied_by = stock.get("supplied_by", None)
                category = stock.get("category")
                item = stock.get("item")
                quantity = stock.get("quantity", None)
                expiry_date = stock.get("expiry_date", None)
                stock_alert = stock.get("stock_alert", None)
                stock_price = stock.get("stock_price", None)
                selling_price = stock.get("selling_price", None)
                has_variants = stock.get("has_variants", False)
                has_unique_ids = stock.get("has_unique_ids", False)
                variants = stock.get("variants", None)

                product = Product.retrieve_product(id=item.id, company=company)
                if product is None:
                    return None
                stocks = cls.upload_stock_and_variant(
                    user=user,
                    company=company,
                    branch=branch,
                    category=category,
                    item=item,
                    supplied_by=supplied_by,
                    quantity=quantity,
                    expiry_date=expiry_date,
                    stock_alert=stock_alert,
                    stock_price=stock_price,
                    selling_price=selling_price,
                    has_variants=has_variants,
                    has_unique_ids=has_unique_ids,
                    variants=variants,
                    comment=comment,
                )
        return True

    @classmethod
    def upload_stock_file(
        cls, user: User, company: Company, branch: Branch, stock_file
    ):
        """
        This class method takes a user, company, branch, and an Excel stock file as input.
        It processes the data in the file, updating or creating stock details for each product.
        """
        stock_file = merge_stock_sheet(stock_file)
        df = pd.DataFrame(stock_file).copy()
        expected_headers = [
            "Category",
            "Sub Category",
            "Supplied by",
            "Item",
            "Brand/Model/Specs",
            "SKU",
            "Description",
            "Quantity",
            "Expiry Date",
            "Stock Alert",
            "Cost Price",
            "Selling Price",
        ]
        column_names = [column for column in df.columns]
        if len(column_names) < len(expected_headers):
            return False

        # Convert date column
        df["Expiry Date"] = pd.to_datetime(df["Expiry Date"], errors="coerce")

        # Process each row individually
        for index, row in df.iterrows():
            # Check required fields
            category_name = row["Category"] if pd.notna(row["Category"]) else None
            product_name = row["Item"] if pd.notna(row["Item"]) else None

            if category_name is None or product_name is None:
                continue  # Skip this row

            # Process optional fields with defaults
            sub_category_name = (
                row["Sub Category"] if pd.notna(row["Sub Category"]) else None
            )
            supplier_name = row["Supplied by"] if pd.notna(row["Supplied by"]) else None
            brand_model_specs = (
                row["Brand/Model/Specs"] if pd.notna(row["Brand/Model/Specs"]) else None
            )
            sku_value = row["SKU"] if pd.notna(row["SKU"]) else None
            description_value = (
                row["Description"] if pd.notna(row["Description"]) else None
            )

            # Handle numeric fields
            try:
                quantity_value = (
                    int(row["Quantity"]) if pd.notna(row["Quantity"]) else 0
                )
            except (ValueError, TypeError):
                quantity_value = 0

            try:
                stock_alert_value = (
                    int(row["Stock Alert"]) if pd.notna(row["Stock Alert"]) else 0
                )
            except (ValueError, TypeError):
                stock_alert_value = 0

            # Handle price fields with comma formatting
            try:
                cost_price_value = (
                    float(str(row["Cost Price"]).replace(",", ""))
                    if pd.notna(row["Cost Price"])
                    else 0.0
                )
            except (ValueError, TypeError, AttributeError):
                cost_price_value = 0.0

            try:
                selling_price_value = (
                    float(str(row["Selling Price"]).replace(",", ""))
                    if pd.notna(row["Selling Price"])
                    else 0.0
                )
            except (ValueError, TypeError, AttributeError):
                selling_price_value = 0.0

            # Get or create category
            category = Category.retrieve_create_category_name(
                name=category_name, user=user, company=company
            )
            # Get or create subcategory
            subcategory = None
            if sub_category_name is not None:
                subcategory, created = SubCategory.objects.update_or_create(
                    company=company,
                    category=category,
                    name=sub_category_name.title(),
                )
            # Get or create product
            product = Product.retrieve_create_product_name(
                name=product_name, user=user, company=company, category=category
            )
            product.subcategory = subcategory
            product.product_price = cost_price_value
            product.selling_price = selling_price_value
            product.sku = sku_value
            product.spec_model_variant = brand_model_specs
            product.product_description = description_value
            product.save()

            supplier = (
                Supplier.retrieve_supplier_name(
                    name=supplier_name, company=company, user=user
                )
                if supplier_name
                else None
            )
            cls.add_stock(
                company=company,
                branch=branch,
                user=user,
                category=category,
                subcategory=subcategory,
                product=product,
                quantity=quantity_value,
                cost_price=cost_price_value,
                selling_price=selling_price_value,
                stock_alert=stock_alert_value,
                supplied_by=supplier,
                sku=sku_value,
            )
        return True

    @classmethod
    def chat_upload_stock(
        cls,
        company: Company,
        branch: Branch,
        user: User,
        category: str,
        product: str,
        quantity: int,
        cost_price: float,
        selling_price: float,
        image: Optional[str] = None,
    ):
        """ """
        category = Category.retrieve_create_category_name(
            name=category, user=user, company=company
        )
        product = Product.retrieve_create_product_name(
            name=product, user=user, company=company, category=category
        )
        cls.add_stock(
            company=company,
            branch=branch,
            user=user,
            category=category,
            product=product,
            quantity=quantity,
            cost_price=cost_price,
            selling_price=selling_price,
            image=image,
        )
        return True

    @classmethod
    def ai_upload_stock(
        cls, company: Company, branch: Branch, user: User, stocks: list
    ):
        """ """
        for data in stocks:
            category = Category.retrieve_create_category_name(
                name=data.get("category"), user=user, company=company
            )
            product = Product.retrieve_create_product_name(
                name=data.get("product"), user=user, company=company, category=category
            )
            cls.add_stock(
                company=company,
                branch=branch,
                user=user,
                category=category,
                product=product,
                quantity=data.get("quantity"),
                cost_price=data.get("cost_price"),
                selling_price=data.get("selling_price"),
            )
        return True


class StockVariant(BaseModel):
    stock_item = models.ForeignKey(
        StockDetail,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    description = models.TextField()
    product_value = models.TextField(blank=True, null=True)
    quantity = models.PositiveIntegerField()
    stock_alert = models.PositiveIntegerField(default=0)
    stock_price = models.DecimalField(
        max_digits=13, decimal_places=2, validators=[MinValueValidator(0.00)]
    )
    selling_price = models.DecimalField(
        max_digits=13, decimal_places=2, validators=[MinValueValidator(0.00)]
    )
    stock_value = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        editable=False,
        validators=[MinValueValidator(0.00)],
    )
    discount = models.FloatField(default=1)
    currency = models.CharField(
        max_length=3, choices=Currency.choices, default=Currency.NAIRA
    )
    supply_history = models.ForeignKey(
        SupplierHistory,
        on_delete=models.CASCADE,
        related_name="supply_variants",
        null=True,
        blank=True,
    )
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE)
    has_unique_ids = models.BooleanField(default=False)

    def __str__(self) -> str:
        if self.stock_item:
            return self.stock_item.__str__()
        elif self.product:
            return self.product.__str__()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "STOCK VARIANT"
        verbose_name_plural = "STOCK VARIANTS"
        constraints = [
            models.UniqueConstraint(
                fields=["company", "branch", "stock_item", "description"],
                name="company_stock_variant",
            )
        ]

    def save(self, *args, **kwargs) -> None:
        self.stock_value = float(self.stock_price) * self.quantity
        super(StockVariant, self).save(*args, **kwargs)

    @property
    def expected_profit(self):
        price = float(self.selling_price)
        if price > 0.00:
            sales_value = price * self.quantity
            return sales_value - float(self.stock_value)
        return 0.00

    @property
    def discounted_selling_price(self):
        price = float(self.selling_price)
        if price > 0.00:
            return price - (price * self.discount)
        return 0.00

    @classmethod
    def upload_variant(
        cls,
        user: User,
        company: Company,
        branch: Branch,
        stock_item: StockDetail,
        supply_history: SupplierHistory,
        variants: list,
    ):
        """
        Creates or updates variant details for a product or an item.
        Args:
            user (User): The user uploading the stock variant details.
            company (Company): The company associated with the stock variant details.
            branch (Branch): The branch associated with the stock variant details.
            variants (list): The list of the product(s) variant(s) uploaded.
        Returns:
            StockVariant: The created or updated stock variant detail objects.
        """
        variants_uploaded = []

        for variant in variants:
            description = variant.get("description", None)
            quantity = variant.get("quantity", None)
            stock_alert = variant.get("stock_alert", None)
            stock_price = variant.get("stock_price", None)
            selling_price = variant.get("selling_price", None)

            variant_detail = cls.objects.filter(
                company=company,
                branch=branch,
                stock_item=stock_item,
                description=description,
            ).first()

            if variant_detail is not None:
                supply_history = supply_history
                variant_detail.quantity = (
                    models.F("quantity") + quantity
                    if quantity is not None
                    else variant_detail.quantity
                )
                variant_detail.stock_alert = (
                    stock_alert
                    if stock_alert is not None
                    else variant_detail.stock_alert
                )
                variant_detail.stock_price = (
                    stock_price
                    if stock_price is not None
                    else variant_detail.stock_price
                )
                variant_detail.selling_price = (
                    selling_price
                    if selling_price is not None
                    else variant_detail.selling_price
                )

                variant_detail.save()
                variants_uploaded.append(variant_detail)
            else:
                variant_detail = cls.objects.create(
                    uploaded_by=user,
                    company=company,
                    branch=branch,
                    stock_item=stock_item,
                    supply_history=supply_history,
                    description=description,
                    quantity=quantity,
                    stock_alert=stock_alert if stock_alert is not None else 0,
                    stock_price=stock_price,
                    selling_price=selling_price,
                )
                variants_uploaded.append(variant_detail)
        return variants_uploaded

    @classmethod
    def upload_product_variant(
        cls,
        user: User,
        company: Company,
        product: Product,
        branch: Branch,
        variants: list,
    ):
        """
        Creates or updates variant details for a product or an item.
        Args:
            user (User): The user uploading the product variant details.
            company (Company): The company associated with the stock variant details.
            variants (list): The list of the product(s) variant(s) uploaded.
        Returns:
            StockVariant: The created or updated stock variant detail objects.
        """
        variants_uploaded = []

        for variant in variants:
            description = variant.get("description", None)
            product_value = variant.get("product_value", None)
            quantity = variant.get("quantity", None)
            variant_price = variant.get("variant_price", None)
            selling_price = variant.get("selling_price", None)
            variant_discount = variant.get("variant_discount", None)
            currency = variant.get("currency", None)

            variant_detail = cls.objects.filter(
                company=company,
                product=product,
                branch=branch,
                description=description,
            ).first()

            if variant_detail is not None:
                variant_detail.quantity = (
                    models.F("quantity") + quantity
                    if quantity is not None
                    else variant_detail.quantity
                )
                variant_detail.discount = (
                    variant_discount
                    if variant_discount is not None
                    else variant_detail.discount
                )
                variant_detail.stock_price = (
                    variant_price
                    if variant_price is not None
                    else variant_detail.stock_price
                )
                variant_detail.selling_price = (
                    selling_price
                    if selling_price is not None
                    else variant_detail.selling_price
                )
                variant_detail.currency = (
                    currency if currency is not None else variant_detail.currency
                )
                variant_detail.product_value = (
                    product_value
                    if product_value is not None
                    else variant_detail.product_value
                )
                variant_detail.save()
                variants_uploaded.append(variant_detail)

            else:
                variant_detail = cls.objects.create(
                    uploaded_by=user,
                    company=company,
                    product=product,
                    description=description,
                    product_value=product_value,
                    quantity=quantity,
                    discount=variant_discount if variant_discount is not None else 0,
                    stock_price=variant_price,
                    selling_price=selling_price,
                    branch=branch,
                    currency=currency,
                )
                variants_uploaded.append(variant_detail)
        return variants_uploaded


class StockUniqueId(BaseModel, DeleteHandler):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE)
    unique_id = models.CharField(max_length=225)
    stock_item = models.ForeignKey(
        StockDetail, on_delete=models.CASCADE, null=True, blank=True
    )
    stock_variant = models.ForeignKey(
        StockVariant, on_delete=models.CASCADE, null=True, blank=True
    )

    def __str__(self) -> str:
        return self.unique_id

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "STOCK UNIQUE ID"
        verbose_name_plural = "STOCK UNIQUE IDENTIFIERS"

    @classmethod
    def upload_unique_ids(
        cls,
        user: User,
        company: Company,
        branch: Branch,
        file,
        stock_item: Optional[StockDetail] = None,
        stock_variant: Optional[StockVariant] = None,
    ):
        """
        Uploads unique IDs from an Excel file and associates them with stock items or variants.
        Args:
            user (User): The user who is uploading the unique IDs.
            company (Company): The company to which the unique IDs are associated.
            branch (Branch): The branch within the company where the unique IDs are relevant.
            file: The Excel file containing unique IDs to be uploaded.
            stock_item (Optional[StockDetail]): The stock item to associate with the unique IDs (if provided).
            stock_variant (Optional[StockVariant]): The stock variant to associate with the unique IDs (if provided).
        Returns:
            bool: True if the upload was successful; False if there are no unique IDs in the file or other errors occur.
        This method reads an Excel file containing unique IDs, associates them with stock items or variants, and stores
        them in the database. The Excel file should have a header row with a column named 'UNIQUE ID' containing the
        unique IDs to be uploaded.
        If stock_item is provided, each unique ID will be associated with a StockDetail object.
        If stock_variant is provided, each unique ID will be associated with a StockVariant object.
        It returns True if the upload was successful, or False if there are no unique IDs in the file.
        """
        excel_workbook = openpyxl.load_workbook(file, read_only=True, data_only=True)
        excel_sheet = excel_workbook.active
        column_names = [cell.value for cell in excel_sheet[1]]
        data_rows = [row for row in excel_sheet.iter_rows(min_row=2, values_only=True)]
        df = pd.DataFrame(data_rows, columns=column_names)
        data = df.to_dict(orient="records")

        for row in data:

            unique_id = row.get("UNIQUE ID")

            if not unique_id:
                return False

            if stock_item is not None:
                cls.objects.create(
                    unique_id=unique_id,
                    stock_item=stock_item,
                    branch=branch,
                    company=company,
                    uploaded_by=user,
                )

            if stock_variant is not None:
                cls.objects.create(
                    unique_id=unique_id,
                    stock_variant=stock_variant,
                    branch=branch,
                    company=company,
                    uploaded_by=user,
                )

        # Flag item as having unique IDs.
        if stock_item is not None:
            stock_item.has_unique_ids = True
            stock_item.save()

        # Flag variant as having unique IDs.
        if stock_variant is not None:
            stock_variant.has_unique_ids = True
            stock_variant.save()

        excel_workbook.close()
        return True


class PriceList(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    subcategory = models.ForeignKey(
        SubCategory,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    item = models.ForeignKey(Product, on_delete=models.CASCADE)
    size_or_volume = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    price = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
    )
    all_branches = models.BooleanField(default=True)
    selected_branches = models.ManyToManyField(Branch, blank=True)
    currency = models.CharField(
        max_length=3,
        choices=Currency.choices,
        default=Currency.NAIRA,
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="updated_price_list",
    )

    def __str__(self) -> str:
        return f"{self.item.name}:  {self.price}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "PRICE LIST DETAIL"
        verbose_name_plural = "PRICE LIST DETAILS"
        constraints = [
            models.UniqueConstraint(
                fields=[
                    "company",
                    "category",
                    "item",
                ],
                name="company_price_list",
            )
        ]

    @classmethod
    @transaction.atomic
    def create_company_price_list(cls, user: User, company: Company, price_lists: list):
        new_price_list_items = []

        for data in price_lists:
            category = data.get("category")
            item = data.get("item")
            size_or_volume = data.get("size_or_volume")
            price = data.get("price")
            all_branches = data.get("all_branches")
            selected_branches = data.get("selected_branches")
            # Check if the price list already exists for the company, category, and item.
            if cls.objects.filter(
                company=company, category=category, item=item
            ).exists():
                raise serializers.ValidationError(
                    {
                        "message": f"Price list for category {category.name} and item {item.name} already exists for the company."
                    }
                )
            item_price = cls.objects.create(
                company=company,
                created_by=user,
                category=category,
                item=item,
                size_or_volume=size_or_volume,
                price=price,
                all_branches=all_branches,
            )
            new_price_list_items.append(item_price)
            if selected_branches:
                item_price.selected_branches.set(selected_branches)
        return new_price_list_items

    @classmethod
    @transaction.atomic
    def pos_create_company_price_list(
        cls,
        user: User,
        company: Company,
        category: Category,
        item: Product,
        price: float,
        branch: Branch,
    ):
        # Check if the price list already exists for the company, category, and item.
        price_list = cls.objects.filter(
            company=company,
            category=category,
            item=item,
        ).last()
        if price_list:
            price_list.price = price
        else:
            price_list = cls.objects.create(
                company=company,
                created_by=user,
                category=category,
                item=item,
                price=price,
            )
        price_list.selected_branches.add(branch)
        price_list.save()
        # update the associated item.
        item.selling_price = price
        item.selected_branches.add(branch)
        item.save()
        return price_list

    @classmethod
    def update_price_list(
        cls,
        user: User,
        company: Company,
        price_list: object,
        size_or_volume: Optional[str] = None,
        price: Optional[float] = None,
        all_branches: Optional[bool] = None,
        selected_branches: Optional[list] = None,
    ):
        # update price list attrs.
        price_list.size_or_volume = (
            size_or_volume if size_or_volume is not None else price_list.size_or_volume
        )
        price_list.price = price if price is not None else price_list.price
        price_list.all_branches = (
            all_branches if all_branches is not None else price_list.all_branches
        )
        (
            price_list.selected_branches.set(selected_branches)
            if selected_branches is not None
            else price_list.selected_branches
        )
        price_list.updated_by = user
        price_list.save()
        # update product attrs.
        Product.objects.filter(
            id=price_list.item.id,
            company=company,
            category=price_list.category,
        ).update(selling_price=price)
        # update inventory attrs.
        StockDetail.objects.filter(
            company=company,
            category=price_list.category,
            item=price_list.item,
        ).update(selling_price=price)
        return price_list


class StockHistory(BaseModel):
    """
    Records the stocks/inventory & sales transaction(s).
    """

    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    subcategory = models.ForeignKey(
        SubCategory,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    item = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity_before = models.IntegerField()
    quantity = models.PositiveIntegerField()
    quantity_after = models.IntegerField()
    transaction_type = models.CharField(
        max_length=25,
        choices=enums.StockHistoryChoices.choices,
    )
    status = models.CharField(
        max_length=25,
        choices=enums.StockHistoryStatusChoices.choices,
    )
    price = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
    )
    stock_value = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="user_stock_history",
    )
    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    comment = models.TextField(null=True, blank=True)

    def __str__(self) -> str:
        return f"{self.company.company_name}: {self.branch.name}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "STOCK HISTORY"
        verbose_name_plural = "STOCK HISTORIES"

    @classmethod
    def stocks_and_sales_comparison(
        cls,
        company: Company,
        result_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ):
        inventory_details = cls.objects.filter(company=company)
        custom_queryset = QuerysetCustomFilter.date_range_filter(
            queryset=inventory_details,
            result_type=result_type,
            start_date=start_date,
            end_date=end_date,
        )
        if isinstance(custom_queryset, QuerySet):
            report = (
                custom_queryset.annotate(
                    month=ExtractMonth("created_at"),
                )
                .values("month")
                .annotate(
                    stocks_value=models.Sum(
                        models.Case(
                            models.When(
                                transaction_type=enums.StockHistoryChoices.STOCK_IN,
                                then="stock_value",
                            ),
                            default=models.Value(0.0),
                            output_field=models.DecimalField(
                                max_digits=20, decimal_places=2
                            ),
                        )
                    ),
                    sales_value=models.Sum(
                        models.Case(
                            models.When(
                                transaction_type=enums.StockHistoryChoices.SALES,
                                then="stock_value",
                            ),
                            default=models.Value(0.0),
                            output_field=models.DecimalField(
                                max_digits=20, decimal_places=2
                            ),
                        )
                    ),
                )
                .values("month", "stocks_value", "sales_value")
                .order_by("month")
            )
            return {"status": True, "details": report}
        return custom_queryset


class StockOut(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)
    requested_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="requested_by"
    )
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    item = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity_requested = models.PositiveIntegerField()
    quantity_approved = models.PositiveIntegerField(default=0)
    status = models.CharField(
        max_length=25,
        choices=enums.RequestStatusChoices.choices,
        default=enums.RequestStatusChoices.PENDING,
    )
    approved_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="approved_by",
    )
    date_approved = models.DateTimeField(null=True, blank=True)
    date_declined = models.DateTimeField(null=True, blank=True)
    request_reason = models.TextField()
    decline_reason = models.TextField(null=True, blank=True)

    def __str__(self) -> str:
        return f"{self.requested_by} requested {self.item}."

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "STOCK OUT"
        verbose_name_plural = "STOCK OUTS"

    @classmethod
    def approve_decline_request(
        cls,
        request_id: str,
        user: User,
        company: Company,
        branch: Branch,
        status: str,
        decline_reason: Optional[str] = None,
    ):
        """
        Approve or decline a stockout request and update its status.
        Args:
            request_id (str): The unique identifier of the stockout request.
            user (User): The user who is approving or declining the request.
            company (Company): The company associated with the request.
            branch (Branch): The branch associated with the request.
            status (str): The new status to set for the stockout request, should be one of ["APPROVED", "DECLINED"].
            decline_reason (Optional[str]): Optional. A reason for declining the request. Only required if status is "DECLINED".
        Returns:
            Union[StockoutRequest, bool, None]: Returns one of the following:
            - If the stockout request is found and updated successfully, it returns the updated StockoutRequest object.
            - If the request status is already in ["APPROVED", "DECLINED"], it returns False to indicate that no action was taken.
            - If the quantity requested is more than the quantity available, it returns False to indicate a re-stock.
            - If the request is not found, it returns None.
        """
        from .utils import deplete_branch_quantity_available, register_stock_history

        action_required = [
            enums.RequestStatusChoices.APPROVED,
            enums.RequestStatusChoices.DECLINED,
        ]
        stockout_request = cls.objects.filter(
            id=request_id, company=company, branch=branch
        ).first()
        if stockout_request is None:
            return {"status": None, "message": "STOCK-OUT request not found."}
        if stockout_request.status in action_required:
            return {"status": False, "message": "no action required."}
        quantity_requested = stockout_request.quantity_requested
        category = stockout_request.category
        product = stockout_request.item
        product_stock_detail = StockDetail.retrieve_branch_item_stock(
            branch=branch, item=product
        )
        if product_stock_detail is None:
            return {
                "status": False,
                "message": "STOCK is not available, kindly re-stock.",
            }
        quantity_available = product_stock_detail.quantity
        if quantity_requested > quantity_available:
            return {
                "status": False,
                "message": "insufficient quantity available, kindly re-stock.",
            }
        action_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        if status == enums.RequestStatusChoices.APPROVED:
            deplete_branch_quantity_available(
                company=company,
                branch=branch,
                category=category,
                item=product,
                quantity=quantity_requested,
                on_hold=False,
            )
            register_stock_history(
                company=company,
                branch=branch,
                category=category,
                item=product,
                price=float(product_stock_detail.stock_price),
                quantity_before=quantity_available,
                quantity=quantity_requested,
                quantity_after=quantity_available - quantity_requested,
                transaction_type=enums.StockHistoryChoices.STOCK_OUT,
                status=enums.StockHistoryStatusChoices.OUTGOING,
                created_by=user,
            )
            stockout_request.date_approved = action_time
            stockout_request.quantity_approved = quantity_requested
        if status == enums.RequestStatusChoices.DECLINED:
            stockout_request.date_declined = action_time
        stockout_request.status = status
        stockout_request.approved_by = user
        stockout_request.decline_reason = decline_reason
        stockout_request.save()
        return {"status": True, "message": stockout_request}


class StockRequest(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    demand_branch = models.ForeignKey(
        Branch, on_delete=models.CASCADE, related_name="demand_branch"
    )
    supply_branch = models.ForeignKey(
        Branch, on_delete=models.CASCADE, related_name="supply_branch"
    )
    requested_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="stock_requested_by"
    )
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    item = models.ForeignKey(Product, on_delete=models.CASCADE)
    request_id = models.CharField(max_length=25)
    quantity_requested = models.PositiveIntegerField()
    quantity_approved = models.PositiveIntegerField(default=0)
    status = models.CharField(
        max_length=25,
        choices=enums.RequestStatusChoices.choices,
        default=enums.RequestStatusChoices.PENDING,
    )
    approved_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="stock_approved_by",
    )
    date_approved = models.DateTimeField(null=True, blank=True)
    date_declined = models.DateTimeField(null=True, blank=True)
    request_reason = models.TextField(null=True, blank=True)
    decline_reason = models.TextField(null=True, blank=True)
    transit_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="stock_transit_by",
    )
    transit_date = models.DateTimeField(null=True, blank=True)
    received_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="stock_received_by",
    )
    received_date = models.DateTimeField(null=True, blank=True)
    image = models.FileField(null=True, blank=True)
    image_url = models.URLField(max_length=2300, null=True, blank=True)
    unique_ids = models.TextField(null=True, blank=True)

    def __str__(self) -> str:
        return f"{self.supply_branch} requested {self.item} from {self.demand_branch}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "STOCK REQUEST"
        verbose_name_plural = "STOCK REQUESTS"

    @classmethod
    @transaction.atomic
    def register_request(
        cls,
        requested_by: User,
        company: Company,
        demand_branch: Branch,
        supply_branch: Branch,
        requests: list,
        request_reason: Optional[str] = None,
    ):
        """
        Registers a new stock request from a supply branch to a demand branch.
        Args:
            requested_by (User): The user who requested the stock.
            company (Company): The company associated with the request.
            supply_branch (Branch): The branch where the stock is supplied to.
            demand_branch (Branch): The branch where the stock is requested from.
            requests (list): A list of request data, each containing category, items, and quantity_requested.
            request_reason (Optional[str]): Optional. A reason for making the request.
        Returns:
            Summary: The summary of the stock request.
        """
        from helpers.reusable_functions import generate_unique_reference

        category_count = set()
        item_count = set()
        total_quantity_requested = 0
        request_id = generate_unique_reference()

        for data in requests:
            category = data.get("category")
            items = data.get("items")

            for product in items:
                item = product.get("item")
                quantity_requested = product.get("quantity")

                category_count.add(category)
                item_count.add(item)
                total_quantity_requested += quantity_requested

                requests = cls.objects.create(
                    company=company,
                    supply_branch=supply_branch,
                    demand_branch=demand_branch,
                    requested_by=requested_by,
                    category=category,
                    item=item,
                    quantity_requested=quantity_requested,
                    request_id=request_id,
                    request_reason=request_reason,
                )
        return {
            "request_id": request_id,
            "category": len(category_count),
            "item": len(item_count),
            "quantity": total_quantity_requested,
            "branch": demand_branch.name,
            "requested_by": requested_by.first_name,
            "date": requests.updated_at,
        }

    @classmethod
    def retrieve_request(cls, id: str, request_id: str):
        """
        Retrieve a stock request using its ID and batch/request ID.
        This method fetches a stock request with the given IDs'.
        Args:
            id (str): The unique ID of the stock request to retrieve.
            request_id (str): The batch ID of the stock request to retrieve.
        Returns:
            StockRequest or None: The retrieved StockRequest object if found, or None if not found.
        """
        try:
            stock_request = cls.objects.get(id=id, request_id=request_id)
        except cls.DoesNotExist:
            stock_request = None
        return stock_request

    @classmethod
    def approve_decline_request(
        cls,
        user: User,
        company: Company,
        branch: Branch,
        request_id: str,
        status: str,
        items: Optional[list] = None,
        decline_reason: Optional[str] = None,
    ):
        """
        Approve or decline a stock request and update its status.
        Args:
            user (User): The user who is approving or declining the request.
            company (Company): The company associated with the request.
            branch (Branch): The branch associated with the request.
            request_id (str): The batch ID of the stock request.
            status (str): The new status to set for the stockout request, should be one of ["APPROVED", "DECLINED"].
            items (Optional[list]): Optional. A list of items and their respective quantity.
            decline_reason (Optional[str]): Optional. A reason for declining the request. Only required if status is "DECLINED".
        Returns:
            Union[StockoutRequest, bool, None]: Returns one of the following:
            - If the stockout request is found and updated successfully, it returns the updated StockoutRequest object.
            - If the request status is already in ["APPROVED", "DECLINED"], it returns False to indicate that no action was taken.
            - If the request is not found, it returns None.
        Note:
        - If items is None, all requests with the request_id will be set to 'DECLINED' (associated fields updated accordingly).
        """
        from stock_inventory.utils import (
            deplete_branch_quantity_available,
            register_stock_history,
        )

        action_required = [
            enums.RequestStatusChoices.APPROVED,
            enums.RequestStatusChoices.DECLINED,
        ]
        stock_requests = cls.objects.filter(company=company, request_id=request_id)
        if not stock_requests.exists():
            return {"status": None, "message": "STOCK REQUEST not found."}

        if stock_requests.first().status in action_required:
            return {
                "status": False,
                "message": "no action required, request already 'DECLINED' or 'APPROVED'.",
            }

        if stock_requests.first().supply_branch == branch:
            return {
                "status": False,
                "message": "Branch cannot approve its own requests.",
            }

        action_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        if items is not None:

            with transaction.atomic():

                for item in items:
                    stock_request = item.get("id")
                    quantity_approved = item.get("quantity_approved")
                    unique_ids = item.get("unique_ids", None)

                    category = stock_request.category
                    product = stock_request.item
                    product_stock_detail = StockDetail.retrieve_branch_item_stock(
                        branch=branch, item=product
                    )
                    if product_stock_detail is None:
                        return {
                            "status": False,
                            "message": "STOCK is not available, kindly re-stock.",
                        }
                    quantity_available = product_stock_detail.quantity
                    if quantity_approved > quantity_available:
                        return {
                            "status": False,
                            "message": "insufficient quantity available, kindly re-stock.",
                        }
                    if status == enums.RequestStatusChoices.APPROVED:
                        if unique_ids is not None:
                            stock_request.unique_ids = ",".join(unique_ids)
                            stock_request.save()
                        deplete_branch_quantity_available(
                            company=company,
                            branch=branch,
                            category=category,
                            item=product,
                            quantity=quantity_approved,
                        )
                        register_stock_history(
                            company=company,
                            branch=branch,
                            category=category,
                            item=product,
                            price=float(product_stock_detail.stock_price),
                            quantity_before=quantity_available,
                            quantity=quantity_approved,
                            quantity_after=quantity_available - quantity_approved,
                            transaction_type=enums.StockHistoryChoices.TRANSFER,
                            status=enums.StockHistoryStatusChoices.OUTGOING,
                            created_by=user,
                        )
                        stock_request.date_approved = action_time
                        stock_request.status = status
                        stock_request.approved_by = user
                        stock_request.quantity_approved = quantity_approved
                        stock_request.save()

                return {"status": True, "data": stock_requests}
        else:
            stock_requests.update(
                status=status,
                date_declined=action_time,
                approved_by=user,
                decline_reason=decline_reason,
            )
            return {"status": True, "data": stock_requests}

    @classmethod
    def transit_request(
        cls,
        user: User,
        company: Company,
        branch: Branch,
        request_id: str,
        status: str,
        image,
    ):
        """
        Update a stock request status to 'TRANSIT'.
        Args:
            user (User): The user who is updating the request.
            company (Company): The company associated with the request.
            branch (Branch): The branch associated with the request.
            request_id (str): The batch ID of the stock request.
            status (str): The new status to set for the stock request, should be 'TRANSIT'.
            image (.jpg, .jpeg, .png): The waybill issuing out the stock request.
        Returns:
            Union[StockRequest, bool, None]: Returns one of the following:
            - If the stockout request is found and updated successfully, it returns the updated StockRequest object(s).
            - If the request status is already in 'DELIVERED', it returns False to indicate that no action was taken.
            - If the request status is already in 'DECLINED', it returns False to indicate that no action was taken.
            - If the request status is already in 'APPROVED', it returns False to indicate that no action was taken.
            - If the request status is already in 'TRANSIT', it returns False to indicate that no action was taken.
            - If the request is not found, it returns None.
        """
        from core.tasks import upload_file_aws_s3_bucket

        stock_requests = cls.objects.filter(company=company, request_id=request_id)
        if not stock_requests.exists():
            return {"status": None, "message": "STOCK REQUEST not found."}

        if stock_requests.first().supply_branch == branch:
            return {
                "status": False,
                "message": "BRANCH cannot update its own requests.",
            }

        if stock_requests.first().status == enums.RequestStatusChoices.DELIVERED:
            return {
                "status": False,
                "message": "request cannot be set to 'TRANSIT', it has been 'DELIVERED'.",
            }

        if stock_requests.first().status == enums.RequestStatusChoices.DECLINED:
            return {
                "status": False,
                "message": "request cannot be set to 'TRANSIT', it has been 'DECLINED'.",
            }

        if stock_requests.first().status != enums.RequestStatusChoices.APPROVED:
            return {
                "status": False,
                "message": "request cannot be set to 'TRANSIT', it has not been 'APPROVED'.",
            }

        if stock_requests.first().status == enums.RequestStatusChoices.TRANSIT:
            return {
                "status": False,
                "message": "no action required, request already in 'TRANSIT'.",
            }

        action_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        stock_requests.update(
            updated_at=action_time,
            status=status,
            transit_date=action_time,
            transit_by=user,
        )
        upload_file_aws_s3_bucket(
            model_instance_id=request_id, file=image, model_name="StockRequest"
        )
        return {"status": True, "data": stock_requests}

    @classmethod
    def receive_request(
        cls,
        user: User,
        company: Company,
        branch: Branch,
        request_id: str,
        status: str,
    ):
        """
        Update a stock request status to 'DELIVERED'.
        Args:
            user (User): The user who is updating the request.
            company (Company): The company associated with the request.
            branch (Branch): The branch associated with the request.
            request_id (str): The batch ID of the stock request.
            status (str): The new status to set for the stock request, should be 'DELIVERED'.
            image (.jpg, .jpeg, .png): The waybill issuing out the stock request.
        Returns:
            Union[StockRequest, bool, None]: Returns one of the following:
            - If the stock request is found and updated successfully, it returns the updated StockRequest object(s).
            - If the request status is already in 'DELIVERED', it returns False to indicate that no action was taken.
            - If the request status is already in 'DECLINED', it returns False to indicate that no action was taken.
            - If the request status is already in 'APPROVED', it returns False to indicate that no action was taken.
            - If the request status is already in 'TRANSIT', it returns False to indicate that no action was taken.
            - If the request is not found, it returns None.
        NOTE:
        - The stock on hold for the branch that supplied the goods is depleted.
        - The stock quantity for the branch that received the goods is increased.
        """
        from .utils import (
            deplete_branch_quantity_on_hold,
            increase_branch_quantity_available,
            register_stock_history,
            transfer_unique_items,
        )

        stock_requests = cls.objects.filter(company=company, request_id=request_id)
        if not stock_requests.exists():
            return {"status": None, "message": "STOCK REQUEST not found."}

        if stock_requests.first().demand_branch == branch:
            return {
                "status": False,
                "message": "BRANCH cannot receive the stock it supplied.",
            }

        if stock_requests.first().status == enums.RequestStatusChoices.DECLINED:
            return {
                "status": False,
                "message": "request cannot be received, it has been 'DECLINED'.",
            }

        if stock_requests.first().status == enums.RequestStatusChoices.DELIVERED:
            return {"status": False, "message": "request has been received already."}

        if stock_requests.first().status != enums.RequestStatusChoices.TRANSIT:
            return {
                "status": False,
                "message": "request cannot be received, it's not in 'TRANSIT' yet.",
            }

        action_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        stock_requests.update(
            updated_at=action_time,
            status=status,
            received_date=action_time,
            received_by=user,
        )

        with transaction.atomic():

            for stock_request in stock_requests:
                category = stock_request.category
                product = stock_request.item
                quantity_approved = stock_request.quantity_approved

                product_stock_detail = StockDetail.retrieve_branch_item_stock(
                    branch=branch, item=product
                )
                if product_stock_detail is not None:
                    quantity_available = product_stock_detail.quantity
                    stock_price = product_stock_detail.stock_price
                else:
                    quantity_available = 0
                    stock_price = 0

                deplete_branch_quantity_on_hold(
                    company=company,
                    branch=stock_request.demand_branch,
                    category=category,
                    item=product,
                    quantity=quantity_approved,
                )
                increase_branch_quantity_available(
                    company=company,
                    branch=branch,
                    category=category,
                    item=product,
                    quantity=quantity_approved,
                    user=user,
                )
                register_stock_history(
                    company=company,
                    branch=branch,
                    category=category,
                    item=product,
                    price=float(stock_price),
                    quantity_before=quantity_available,
                    quantity=quantity_approved,
                    quantity_after=quantity_available + quantity_approved,
                    transaction_type=enums.StockHistoryChoices.REQUEST,
                    status=enums.StockHistoryStatusChoices.INCOMING,
                    created_by=user,
                )
                if stock_request.unique_ids is not None:
                    transfer_unique_items(
                        user=user,
                        company=company,
                        from_branch=stock_request.demand_branch,
                        to_branch=branch,
                        unique_ids=stock_request.unique_ids.split(","),
                        stock_item=product_stock_detail,
                    )
            return {"status": True, "data": stock_requests}


class InventoryJournal(BaseModel):
    type = models.CharField(
        max_length=25,
        choices=enums.InventoryJournalChoices.choices,
    )
    item = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        editable=False,
    )
    quantity = models.PositiveIntegerField(
        default=0,
        editable=False,
    )
    stock_value = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        editable=False,
        validators=[MinValueValidator(0.0)],
    )
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, editable=False)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, editable=False)

    def __str__(self) -> str:
        return f"{self.type} {self.item} = {self.quantity}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "INVENTORY JOURNAL"
        verbose_name_plural = "INVENTORY JOURNALS"


class InventoryStatus(BaseModel):
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        editable=False,
    )
    branch = models.ForeignKey(
        Branch,
        on_delete=models.CASCADE,
        editable=False,
    )
    item = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        editable=False,
    )
    opening_quantity = models.PositiveIntegerField(
        default=0,
        editable=False,
    )
    closing_quantity = models.PositiveIntegerField(
        default=0,
        editable=False,
    )
    opening_value = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
        editable=False,
    )
    closing_value = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
        editable=False,
    )

    def __str__(self) -> str:
        return f"{self.item.name} valued at: {self.closing_value}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "INVENTORY STATUS"
        verbose_name_plural = "INVENTORY STATUSES"


class PurchaseOrder(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)
    requested_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="purchase_order_requested_by",
    )
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    item = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity_requested = models.PositiveIntegerField()
    quantity_delivered = models.PositiveIntegerField(default=0)
    status = models.CharField(
        max_length=25,
        choices=enums.RequestStatusChoices.choices,
        default=enums.RequestStatusChoices.PENDING,
    )
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE)
    date_delivered = models.DateTimeField(null=True, blank=True)
    request_id = models.CharField(max_length=25)

    def __str__(self) -> str:
        return self.branch.__str__()

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "PURCHASE ORDER"
        verbose_name_plural = "PURCHASE ORDERS"

    @classmethod
    def register_order(
        cls,
        user: User,
        company: Company,
        branch: Branch,
        supplier: Supplier,
        orders: list,
    ):
        """ """
        from helpers.reusable_functions import generate_unique_reference
        from stock_inventory.tasks import supplier_purchase_order

        category_count = set()
        item_count = set()
        total_quantity = 0
        request_id = generate_unique_reference()

        for order in orders:
            category = order.get("category")
            product = order.get("item")
            quantity = order.get("quantity")

            category_count.add(category)
            item_count.add(product)
            total_quantity += quantity

            purchase_order = cls.objects.create(
                company=company,
                branch=branch,
                requested_by=user,
                category=category,
                item=product,
                quantity_requested=quantity,
                supplier=supplier,
                request_id=request_id,
            )
        supplier_purchase_order.delay(request_id=request_id)
        return {
            "request_id": request_id,
            "category": len(category_count),
            "item": len(item_count),
            "quantity": total_quantity,
            "branch": branch.name,
            "requested_by": user.first_name,
            "date": purchase_order.created_at,
        }


class StockTransfer(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)
    transfer_to = models.ForeignKey(
        Branch, on_delete=models.CASCADE, related_name="transfer_to"
    )
    approved_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="stock_transfer_approved_by"
    )
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    item = models.ForeignKey(Product, on_delete=models.CASCADE)
    variant = models.ForeignKey(
        StockVariant, on_delete=models.CASCADE, null=True, blank=True
    )
    quantity = models.PositiveIntegerField()
    stock_price = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        editable=False,
        validators=[MinValueValidator(0.00)],
    )
    selling_price = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.00)],
        default=0.00,
    )
    status = models.CharField(
        max_length=25,
        choices=enums.RequestStatusChoices.choices,
        default=enums.RequestStatusChoices.PENDING,
    )
    request_id = models.CharField(max_length=25)
    received_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="stock_transfer_received_by",
        null=True,
        blank=True,
    )
    image = models.FileField(null=True, blank=True)
    image_url = models.URLField(max_length=2300, null=True, blank=True)

    def __str__(self) -> str:
        return f"{self.branch} transferred {self.quantity} {self.item} to {self.transfer_to}."

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "STOCK TRANSFER"
        verbose_name_plural = "STOCK TRANSFERS"

    @classmethod
    @transaction.atomic
    def register_transfer(
        cls,
        user: User,
        company: Company,
        branch: Branch,
        transfer_to: Branch,
        transfers: list,
    ):
        from helpers.reusable_functions import generate_unique_reference
        from stock_inventory.utils import (
            deplete_branch_quantity_available,
            increase_branch_quantity_available,
            register_stock_history,
        )

        """ """

        category_count = set()
        item_count = set()
        total_quantity = 0
        request_id = generate_unique_reference()

        for transfer in transfers:
            category = transfer.get("category")
            product = transfer.get("item")
            quantity = transfer.get("quantity")
            price = transfer.get("stock_price")
            selling_price = transfer.get("selling_price")

            stock_transfer = deplete_branch_quantity_available(
                company=company,
                branch=branch,
                category=category,
                item=product,
                quantity=quantity,
                on_hold=False,
                transfer=True,
            )
            if stock_transfer.get("status") is False:
                return {
                    "status": False,
                    "message": stock_transfer.get("message"),
                }
            category_count.add(category)
            item_count.add(product)
            total_quantity += quantity

            transfers = cls.objects.create(
                company=company,
                branch=branch,
                transfer_to=transfer_to,
                approved_by=user,
                category=category,
                item=product,
                variant=transfer.get("variant", None),
                quantity=quantity,
                request_id=request_id,
                stock_price=price,
                selling_price=selling_price,
                status=enums.RequestStatusChoices.APPROVED,
            )
            register_stock_history(
                company=company,
                branch=branch,
                category=category,
                item=product,
                price=float(price),
                quantity_before=stock_transfer.get("quantity_before"),
                quantity=quantity,
                quantity_after=stock_transfer.get("quantity_after"),
                transaction_type=enums.StockHistoryChoices.TRANSFER,
                status=enums.StockHistoryStatusChoices.OUTGOING,
                created_by=user,
            )
            # Transfer to inventory details.
            stock_in = increase_branch_quantity_available(
                company=company,
                branch=transfer_to,
                category=category,
                item=product,
                quantity=quantity,
                user=user,
            )
            quantity_before, quantity_after = stock_in
            register_stock_history(
                company=company,
                branch=transfer_to,
                category=category,
                item=product,
                price=float(price),
                quantity_before=quantity_before,
                quantity=quantity,
                quantity_after=quantity_after,
                transaction_type=enums.StockHistoryChoices.TRANSFER,
                status=enums.StockHistoryStatusChoices.INCOMING,
                created_by=user,
            )
        return {
            "status": True,
            "message": {
                "request_id": request_id,
                "category": len(category_count),
                "item": len(item_count),
                "quantity": total_quantity,
                "branch": branch.name,
                "transfer_to": transfer_to.name,
                "approved_by": user.first_name,
                "date": transfers.created_at,
            },
        }

    @classmethod
    def transfer_transition(
        cls, company: Company, branch: Branch, request_id: str, image
    ):
        """
        Update a stock request status to 'TRANSIT'.
        Args:
            company (Company): The company associated with the request.
            branch (Branch): The branch associated with the request.
            request_id (str): The batch ID of the stock request.
            image (.jpg, .jpeg, .png): The waybill issuing out the stock request.
        Returns:
            Union[StockRequest, bool, None]: Returns one of the following:
            - If the stockout request is found and updated successfully, it returns the updated StockRequest object(s).
            - If the request status is already in 'DELIVERED', it returns False to indicate that no action was taken.
            - If the request status is already in 'DECLINED', it returns False to indicate that no action was taken.
            - If the request status is already in 'APPROVED', it returns False to indicate that no action was taken.
            - If the request status is already in 'TRANSIT', it returns False to indicate that no action was taken.
            - If the request is not found, it returns None.
        """
        from core.tasks import upload_file_aws_s3_bucket

        stock_transfer = cls.objects.filter(
            company=company, branch=branch, request_id=request_id
        )
        if not stock_transfer.exists():
            return {"status": None, "message": "STOCK TRANSFER not found."}
        if stock_transfer.first().status == enums.RequestStatusChoices.DELIVERED:
            return {
                "status": False,
                "message": "transfer cannot be set to 'TRANSIT', it has been 'DELIVERED'.",
            }
        if stock_transfer.first().status != enums.RequestStatusChoices.APPROVED:
            return {
                "status": False,
                "message": "transfer cannot be set to 'TRANSIT', it has not been 'APPROVED'.",
            }
        if stock_transfer.first().status == enums.RequestStatusChoices.TRANSIT:
            return {
                "status": False,
                "message": "no action required, request already in 'TRANSIT'.",
            }
        action_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        stock_transfer.update(
            updated_at=action_time,
            status=enums.RequestStatusChoices.TRANSIT,
        )
        upload_file_aws_s3_bucket(
            model_instance_id=request_id, file=image, model_name="StockTransfer"
        )
        return {"status": True, "data": stock_transfer}


class StockOnHoldHistory(BaseModel, DeleteHandler):
    from sales_app.models import SalesTransaction

    """
    NOTE:
    - if the stock is still on hold the record will be active; and it will not indicate deleted.
    - if the stock is not on hold the record will not be active; and it will indicate deleted.
    """
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
    )
    branch = models.ForeignKey(
        Branch,
        on_delete=models.CASCADE,
    )
    category = models.ForeignKey(
        Category,
        on_delete=models.CASCADE,
    )
    item = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
    )
    quantity = models.PositiveIntegerField()
    sales_transaction = models.ForeignKey(
        SalesTransaction,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    stock_out = models.ForeignKey(
        StockOut,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    stock_request = models.ForeignKey(
        StockRequest,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    stock_transfer = models.ForeignKey(
        StockTransfer,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "STOCK ON HOLD HISTORY"
        verbose_name_plural = "STOCK ON HOLD HISTORIES"

    def __str__(self) -> str:
        return f"{self.branch.name}: {self.quantity} {self.item.name} on hold"


class CompanyStore(BaseModel, DeleteHandler):
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="company_store",
    )
    store_brand_color = models.CharField(max_length=20)
    store_description = models.TextField(null=True, blank=True)
    store_url = models.CharField(max_length=255)
    header_alignment = models.CharField(
        max_length=10, choices=AlignmentTypes.choices, default=AlignmentTypes.TOP
    )
    header_image = models.URLField(max_length=2300, null=True, blank=True)
    header_logo = models.URLField(max_length=2300, null=True, blank=True)
    header_logo_text = models.CharField(max_length=255, null=True, blank=True)
    navigation_visible = models.BooleanField(default=False)
    navigation_set_menu = models.ManyToManyField(
        "stock_inventory.Category",
        blank=True,
    )
    navigation_alignment = models.CharField(
        max_length=10, choices=AlignmentTypes.choices, default=AlignmentTypes.TOP
    )
    contact_visible = models.BooleanField(default=False)
    contact_phone_number = models.CharField(max_length=10, null=True, blank=True)
    contact_email = models.EmailField(null=True, blank=True)
    contact_address = models.TextField(null=True, blank=True)
    redirect_phone_number = models.CharField(max_length=10, null=True, blank=True)
    interface_theme = models.CharField(
        max_length=10, choices=InterfaceTheme.choices, default=InterfaceTheme.SYSTEM
    )
    order_completion_message = models.TextField(null=True, blank=True)
    redirect_after_payment_url = models.URLField(null=True, blank=True)
    success_message_url = models.URLField(null=True, blank=True)
    notification_url = models.URLField(null=True, blank=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)

    def __str__(self) -> str:
        return f"{self.company.company_name}: {self.store_url}"


class FinishedProduct(BaseModel):
    name = models.CharField(max_length=255, unique=True)
    company = models.ForeignKey(Company, null=True, on_delete=models.SET_NULL)

    def __str__(self) -> str:
        return self.name


class FinishedProductRecipe(BaseModel):
    finished_product = models.ForeignKey(
        FinishedProduct,
        on_delete=models.CASCADE,
        related_name="recipes",
        null=True,
        blank=True,
    )
    product = models.ForeignKey("Product", on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField()

    def __str__(self) -> str:
        return f"{self.finished_product.name} - {self.product.name}"


class APILog(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="stock_logger"
    )
    method = models.CharField(max_length=10, choices=RequestMethodType.choices)
    path = models.CharField(max_length=255)
    request_headers = models.TextField(null=True, blank=True)
    request_body = models.TextField(null=True, blank=True)
    response_body = models.TextField(null=True, blank=True)
    items_depletion_history = models.TextField(null=True, blank=True)
    status_code = models.CharField(max_length=3, null=True, blank=True)

    def __str__(self):
        return f"{self.sales_batch_id} - {self.status_code}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "API LOG"
        verbose_name_plural = "API LOGS"


class PriceTag(BaseModel):
    system_default = models.BooleanField(default=False, editable=False)
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="added_price_tag",
    )
    name = models.CharField(max_length=255)
    updated_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="updated_price_tag",
    )

    def save(self, *args, **kwargs) -> None:
        self.name = strip_all_whitespaces(self.name).lower()
        super(PriceTag, self).save(*args, **kwargs)

    def __str__(self):
        return f"{self.company}: {self.name}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "PRICE TAG"
        verbose_name_plural = "PRICE TAGS"
        constraints = [
            models.UniqueConstraint(
                fields=["company", "name"],
                name="unique_company_price_tag",
            )
        ]


class PriceVariation(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    item = models.ForeignKey(Product, on_delete=models.CASCADE)
    price_tag = models.ForeignKey(PriceTag, on_delete=models.CASCADE)
    method = models.CharField(
        max_length=25,
        choices=enums.PriceVariationMethods.choices,
        default=enums.PriceVariationMethods.FIXED,
        editable=False,
    )
    percentage = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0)],
        editable=False,
    )
    price = models.DecimalField(
        max_digits=13,
        decimal_places=2,
        validators=[MinValueValidator(0.0)],
        default=0.0,
        editable=False,
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    def __str__(self):
        return f"{self.company} => {self.item} => {self.price_tag} => {self.method} => {self.price}"

    def save(self, *args, **kwargs) -> None:
        if self.method == enums.PriceVariationMethods.MARGIN:
            self.price = self.margin
        if self.method == enums.PriceVariationMethods.MARK_DOWN:
            self.price = self.mark_down
        if self.method == enums.PriceVariationMethods.MARK_UP:
            self.price = self.mark_up
        super(PriceVariation, self).save(*args, **kwargs)

    @property
    def margin(self):
        return round(
            (float(self.item.product_price) / (1 - (self.percentage / 100))), 2
        )

    @property
    def mark_down(self):
        return round(
            (float(self.item.selling_price) * (1 - (self.percentage / 100))), 2
        )

    @property
    def mark_up(self):
        return round(
            (float(self.item.product_price) * (1 + (self.percentage / 100))), 2
        )

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "PRICE VARIATION"
        verbose_name_plural = "PRICE VARIATIONS"
        constraints = [
            models.UniqueConstraint(
                fields=["company", "item", "price_tag"],
                name="unique_company_price_variation",
            )
        ]

    @classmethod
    def manage_prices(
        cls,
        user: User,
        company: Company,
        product: Product,
        product_price: float,
        selling_price: float,
        method: str,
        price_variations: list,
    ):
        # update the product attrs.
        product.product_price = product_price
        product.selling_price = selling_price
        product.updated_by = user
        product.save()
        # update the price list attrs.
        PriceList.objects.filter(
            company=company, category=product.category, item=product
        ).update(
            price=selling_price,
            updated_by=user,
        )
        # update the inventory attrs.
        StockDetail.objects.filter(
            company=company, category=product.category, item=product
        ).update(
            stock_price=product_price,
            selling_price=selling_price,
            updated_by=user,
        )
        if method == enums.PriceVariationMethods.FIXED:
            for variation in price_variations:
                price_variation = cls.objects.filter(
                    company=company,
                    item=product,
                    price_tag=variation["price_tag"],
                ).first()
                if price_variation is not None:
                    price_variation.method = method
                    price_variation.price = variation["price"]
                    price_variation.updated_by = user
                    price_variation.save()
        else:
            for variation in price_variations:
                price_variation = cls.objects.filter(
                    company=company,
                    item=product,
                    price_tag=variation["price_tag"],
                ).first()
                if price_variation is not None:
                    price_variation.method = method
                    price_variation.percentage = variation["percentage"]
                    price_variation.updated_by = user
                    price_variation.save()
        return True


class Brand(BaseModel):
    name = models.CharField(max_length=200)
    image = models.ImageField(upload_to="brand-images", null=True, blank=True)

    def __str__(self):
        return self.name


class ProductType(BaseModel):
    name = models.CharField(max_length=200)
    image = models.ImageField(upload_to="product-type-images", blank=True, null=True)
    category = models.ForeignKey(SubCategory, on_delete=models.CASCADE)
    percentage_commission = models.DecimalField(
        max_digits=50, decimal_places=2, default=0, null=True, blank=True
    )
    fixed_commission = models.DecimalField(
        max_digits=50, decimal_places=2, default=0, null=True, blank=True
    )
    commission_applicable = models.BooleanField(default=False)

    def __str__(self):
        return self.name


class SupplierProduct(BaseModel):
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, null=True)
    brand = models.ForeignKey(Brand, on_delete=models.SET_NULL, null=True, blank=True)
    name = models.CharField(max_length=200)
    description = models.TextField(help_text="Describe the product")
    category = models.ForeignKey(
        Category, on_delete=models.SET_NULL, blank=True, null=True
    )
    sub_category = models.ForeignKey(
        SubCategory, blank=True, null=True, on_delete=models.SET_NULL
    )
    product_type = models.ForeignKey(
        ProductType, on_delete=models.SET_NULL, null=True, blank=True
    )
    tags = models.TextField(blank=True, null=True)
    model = models.CharField(blank=True, null=True, max_length=50)
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)
    out_of_stock = models.BooleanField(default=False)
    published = models.BooleanField(default=False)
    view_count = models.PositiveBigIntegerField(
        default=0, help_text="Total times the items was viewed by users"
    )
    sale_count = models.IntegerField(default=0, help_text="Total number sold")
    published_on = models.DateTimeField(blank=True, null=True)

    def __str__(self):
        return self.name


class SupplierProductDetail(BaseModel):
    product = models.ForeignKey(SupplierProduct, on_delete=models.CASCADE)
    sku = models.CharField(max_length=100, blank=True, null=True)
    size = models.CharField(max_length=100, blank=True, null=True)
    color = models.CharField(max_length=100, default="White", blank=True, null=True)
    weight = models.FloatField(default=0, blank=True, null=True)
    length = models.FloatField(default=0, blank=True, null=True)
    width = models.FloatField(default=0, blank=True, null=True)
    height = models.FloatField(default=0, blank=True, null=True)
    stock = models.IntegerField(default=1, blank=True, null=True)
    price = models.FloatField(default=0)
    discount = models.FloatField(default=0, blank=True, null=True)
    low_stock_threshold = models.IntegerField(default=5, blank=True, null=True)
    shipping_days = models.PositiveIntegerField(default=3, blank=True, null=True)
    out_of_stock_date = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.id}: {self.product}"


class SupplierProductImage(BaseModel):
    product = models.ForeignKey(SupplierProduct, on_delete=models.CASCADE)
    image = models.TextField(blank=True, null=True)
    is_primary = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.product.name}: {self.id}"


class SupplierProductReview(models.Model):
    # reviewed_by = models.ForeignKey(User, on_delete=models.CASCADE)
    reviewed_by = models.CharField(max_length=100)
    product = models.ForeignKey(SupplierProduct, on_delete=models.CASCADE)
    rating = models.IntegerField(default=0)
    headline = models.CharField(max_length=250)
    text = models.TextField()
    created_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return "{} {}".format(self.reviewed_by, self.product)
