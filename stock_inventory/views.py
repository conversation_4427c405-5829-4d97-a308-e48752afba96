from datetime import datetime
import json
import os

from django.conf import settings
from django.db import transaction
from django.db.models import Count, F, Min, Q, Sum
from django.db.models.functions import TruncDate
from django.db.models.query import QuerySet
from django.http import FileResponse
import pytz
from rest_framework import status, generics
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response as APIResponse
from rest_framework.views import APIView

from cart_management.models import Order, OrderProduct
from cart_management.serializers import ProductOrderSerializer
from core.auth.custom_auth import CustomUserAuthentication
from core.exceptions import raise_serializer_error_msg
from core.pagenator import CustomPagination
from core.permissions import IpWhiteListPermission
from helpers.ai import process_price_list, process_stock_items
from helpers.custom_filters import QuerysetCustomFilter
from helpers.custom_permissions import (
    BranchRequiredPermission,
    CompanyOwnerPermission,
    CompanyRequiredPermission,
)
from helpers.custom_response import Response
from helpers.reusable_functions import (
    Paginator,
    UserIdentifier,
    format_string_to_date_object,
    is_valid_string,
    is_valid_uuid,
)
from requisition.models import Company, Team, TeamMember
from requisition.serializers import SupplierSerializerOut
from sales_app.models import SalesTransaction, SalesTransactionItem
from sales_app.helper import enums
from stock_inventory import models, serializers, utils
from stock_inventory.helper import enums as stock_enums


# Create your view(s) here.
class UserCompaniesAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        user = request.user
        teams = Team.objects.filter(members__member=user)
        companies = Company.objects.filter(Q(user=user) | Q(teams__in=teams)).distinct()
        if not companies.exists():
            data = {
                "message": "USER is not associated with any companies.",
                "companies": [],
            }
        else:
            user_companies = [
                {
                    "company_name": company.company_name,
                    "company_id": company.id,
                    "user_status": "OWNER" if company.user == user else "MEMBER",
                }
                for company in companies
            ]
            data = {
                "message": "successfully fetched USER's companies.",
                "companies": user_companies,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CompanyOverviewAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company_id")
        company = Company.objects.filter(id=company_id).first()
        company_branches = models.Branch.objects.filter(company=company)
        company_teams = Team.objects.filter(company=company, is_active=True)
        company_stock_details = models.StockDetail.objects.filter(company=company)
        company_stock_value = (
            company_stock_details.aggregate(total_stock_value=Sum("stock_value"))[
                "total_stock_value"
            ]
            or 0.0
        )
        company_expected_profit = sum(
            [stock.expected_profit for stock in company_stock_details]
        )
        total_sales_transaction = (
            SalesTransaction.objects.filter(
                company=company, status=enums.TransactionStatusChoices.SUCCESSFUL
            ).aggregate(amount=Sum("total_sales_amount"))["amount"]
            or 0.0
        )
        data = {
            "company": company.company_name,
            "branches": company_branches.count(),
            "teams": company_teams.count(),
            "total_stock_value": company_stock_value,
            "total_sales_values": total_sales_transaction,
            "total_expected_profit": company_expected_profit,
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CompanyProductOverviewAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]
    serializer_class = serializers.CompanyProductSerializer

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        search = request.query_params.get("search")
        company_products = models.Product.objects.filter(company=company_id)
        if search and is_valid_string(search):
            company_products = company_products.filter(
                Q(name__icontains=search) | Q(category__name__icontains=search)
            )
        paginated_response = Paginator.paginate(
            request=request, queryset=company_products
        )
        serializer = self.serializer_class(instance=paginated_response, many=True)
        data = {
            "message": "successfully fetched company's products.",
            "products": serializer.data,
            "count": len(serializer.data),
            "total_products": company_products.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CompaniesOverviewAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        user_companies = UserIdentifier.get_company_ownership(user=request.user)
        if user_companies is None:
            data = {
                "companies": 0,
                "branches": 0,
                "team_members": 0,
                "total_stock": 0,
                "stock_value": 0.0,
            }
        else:
            companies_branches = models.Branch.objects.filter(
                company__in=user_companies
            )
            companies_teams = Team.objects.filter(
                company__in=user_companies, is_active=True
            )
            companies_teams_members = (
                TeamMember.objects.filter(
                    team__in=companies_teams,
                    is_active=True,
                )
                .order_by("member__id")
                .distinct("member")
            )
            companies_stock_details = models.StockDetail.objects.filter(
                company__in=user_companies
            ).aggregate(
                total_stock=Sum("quantity"),
                total_stock_value=Sum("stock_value"),
            )
            data = {
                "companies": user_companies.count(),
                "branches": companies_branches.count(),
                "team_members": companies_teams_members.count(),
                "total_stock": companies_stock_details.get("total_stock") or 0,
                "stock_value": companies_stock_details.get("total_stock_value") or 0.00,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class UserCompanyBranchesOverviewAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.BranchesOverviewSerializer

    def get(self, request, *args, **kwargs):
        user_companies = UserIdentifier.get_company_ownership(user=request.user)
        if user_companies is None:
            data = {
                "message": "user has no company yet.",
                "companies": [],
                "count": 0,
                "total_companies": 0,
            }
        else:
            paginated_response = Paginator.paginate(
                request=request, queryset=user_companies
            )
            serializer = self.serializer_class(instance=paginated_response, many=True)
            data = {
                "message": "successfully fetched companies & branches details.",
                "companies": serializer.data,
                "count": len(serializer.data),
                "total_companies": user_companies.count(),
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CompanyCategoryOverviewAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branches = models.Branch.objects.filter(company=company_id)
        paginated_response = Paginator.paginate(request=request, queryset=branches)
        serializer = serializers.CompanyBranchesStockCountSerializer(
            instance=paginated_response, many=True
        )
        data = {
            "message": "successfully fetched company's categories stock count.",
            "branches": serializer.data,
            "count": len(serializer.data),
            "total_branches": branches.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchCreateAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.BranchSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        branch = models.Branch.create_company_branch(
            user=request.user, **serializer.validated_data
        )
        if branch is False:
            return Response(
                errors={"message": "duplicate branch, try again."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = self.serializer_class(
            instance=branch, context={"user": request.user}
        )
        data = {
            "message": "branch created successfully.",
            "branch": serializer.data,
        }
        return Response(data=data, status_code=201, status=status.HTTP_201_CREATED)


class BranchAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]
    serializer_class = serializers.BranchSerializer
    update_serializer_class = serializers.BranchUpdateSerializer

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")

        company_branches = models.Branch.objects.filter(company=company_id)
        if branch_id:
            if (
                not is_valid_uuid(branch_id)
                or not company_branches.filter(id=branch_id).exists()
            ):
                return Response(
                    errors={"message": "provide a valid branch ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            branch = company_branches.filter(id=branch_id).first()
            serializer = self.serializer_class(
                instance=branch, context={"user": request.user}
            )
            data = {
                "message": "successfully fetched company's branch.",
                "branch": serializer.data,
            }
        else:
            paginated_response = Paginator.paginate(
                request=request, queryset=company_branches
            )
            serializer = self.serializer_class(
                instance=paginated_response,
                context={"user": request.user},
                many=True,
            )
            data = {
                "message": "successfully fetched company's branches.",
                "branches": serializer.data,
                "count": len(serializer.data),
                "total_branches": company_branches.count(),
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)

    def patch(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        data = request.data
        data["updated_by"] = request.user.id
        if branch_id is None or not is_valid_uuid(branch_id):
            return Response(
                errors={"message": "provide a valid branch ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        branch = models.Branch.objects.filter(company=company, id=branch_id).first()
        if branch is None:
            return Response(
                errors={"message": "provide a valid branch ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = self.update_serializer_class(branch, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(
            data=serializer.data,
            status_code=200,
            status=status.HTTP_200_OK,
        )

    def delete(self, request, *args, **kwargs):
        serializer = serializers.BranchDeleteSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        branch = models.Branch.destroy(**serializer.validated_data)
        if not branch.get("status"):
            return Response(
                errors=branch, status_code=400, status=status.HTTP_400_BAD_REQUEST
            )
        return Response(
            data={"status": True, "message": "branch deleted successfully."},
            status_code=204,
            status=status.HTTP_204_NO_CONTENT,
        )


class BranchWithInstantWebAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated, CompanyRequiredPermission]
    serializer_class = serializers.BranchSerializer

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")

        # Filter branches that have an associated InstantWeb store
        branches_with_instant_web = models.Branch.objects.filter(
            company_id=company_id, Instant_web_store__isnull=False
        ).distinct()

        paginated_response = Paginator.paginate(
            request=request, queryset=branches_with_instant_web
        )

        serializer = self.serializer_class(
            instance=paginated_response, context={"user": request.user}, many=True
        )
        data = {
            "message": "successfully fetched company's branches with instant web store.",
            "branches": serializer.data,
            "count": len(serializer.data),
            "total_branches_with_instant_web": branches_with_instant_web.count(),
        }
        return Response(data=data, status=status.HTTP_200_OK)


class CategoriesAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]
    serializer_class = serializers.CategorySerializer
    create_serializer_class = serializers.CategoryCreateSerializer
    delete_serializer_class = serializers.CategoryDeleteSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.create_serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        company_categories = models.Category.create_company_categories(
            user=request.user, **serializer.validated_data
        )
        serializer = self.serializer_class(company_categories, many=True)
        data = {
            "message": "successfully added categories.",
            "categories": serializer.data,
        }
        return Response(data=data, status_code=201, status=status.HTTP_201_CREATED)

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        category_id = request.query_params.get("category")
        search = request.query_params.get("search")

        available_categories = models.Category.objects.filter(
            company=company_id,
            is_active=True,
        )
        if search and is_valid_string(search):
            available_categories = available_categories.filter(
                name__icontains=search
            ).order_by("name")
        if category_id:
            if not is_valid_uuid(category_id):
                return Response(
                    errors={"message": "provide a valid category ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            available_categories = available_categories.filter(id=category_id)
            if not available_categories.exists():
                return Response(
                    errors={"message": "category not found."},
                    status_code=404,
                    status=status.HTTP_404_NOT_FOUND,
                )
        if branch_id:
            if not is_valid_uuid(branch_id) or models.Branch.retrieve_company_branch(
                id=branch_id, company=company_id
            ):
                return Response(
                    errors={"message": "provide a valid branch ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
        branch = None
        paginated_data = Paginator.paginate(
            request=request, queryset=available_categories
        )
        serializer = self.serializer_class(
            instance=paginated_data,
            many=True,
            context={
                "company": company_id,
                "branch": branch,
            },
        )
        data = {
            "message": "successfully fetched categories.",
            "categories": serializer.data,
            "count": len(serializer.data),
            "total_categories": available_categories.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)

    def patch(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        category_id = request.query_params.get("category")
        data = request.data
        data["updated_by"] = request.user.id
        if category_id is None or not is_valid_uuid(category_id):
            return Response(
                errors={"message": "provide a valid category ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        category = models.Category.objects.filter(
            id=category_id,
            company=company,
            is_active=True,
        ).first()
        if category is None:
            return Response(
                errors={"message": "provide a valid category ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = self.serializer_class(category, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(
            data=serializer.data,
            status_code=200,
            status=status.HTTP_200_OK,
        )

    def delete(self, request, *args, **kwargs):
        serializer = self.delete_serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        destroy = utils.destroy_company_category(**serializer.validated_data)
        return Response(
            data={"message": destroy.get("message")},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class SubCategoriesAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]
    serializer_class = serializers.SubcategorySerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(created_by=request.user)
        data = {
            "message": "successfully added subcategories.",
            "subcategories": serializer.data,
        }
        return Response(data=data, status_code=201, status=status.HTTP_201_CREATED)

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        category_id = request.query_params.get("category")
        subcategory_id = request.query_params.get("subcategory")
        search = request.query_params.get("search")

        available_subcategories = models.SubCategory.objects.filter(
            company=company_id,
            is_active=True,
        )
        if category_id and is_valid_uuid(category_id):
            available_subcategories = available_subcategories.filter(
                category=category_id
            )
        if subcategory_id and is_valid_uuid(subcategory_id):
            available_subcategories = available_subcategories.filter(id=subcategory_id)
        if search and is_valid_string(search):
            available_subcategories = available_subcategories.filter(
                Q(name__icontains=search) | Q(category__name__icontains=search)
            ).order_by("name")
        paginated_data = Paginator.paginate(
            request=request, queryset=available_subcategories
        )
        serializer = self.serializer_class(instance=paginated_data, many=True)
        data = {
            "message": "successfully fetched subcategories.",
            "subcategories": serializer.data,
            "count": len(serializer.data),
            "total_subcategories": available_subcategories.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)

    def delete(self, request, *args, **kwargs):
        serializer = serializers.SubCategoryDeleteSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        destroy = utils.destroy_company_subcategory(**serializer.validated_data)
        return Response(
            data={"message": destroy.get("message")},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class ProductAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]
    serializer_class = serializers.ProductDetailsSerializer
    create_serializer_class = serializers.ProductCreateSerializer
    delete_serializer_class = serializers.ProductDeleteSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.create_serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        product = models.Product.create_company_product(
            user=request.user, **serializer.validated_data
        )
        return Response(
            data={"message": "success.", "products": product},
            status_code=201,
            status=status.HTTP_201_CREATED,
        )

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        category_id = request.query_params.get("category")
        subcategory_id = request.query_params.get("subcategory")
        product_id = request.query_params.get("product")
        search = request.query_params.get("search")

        company_products = models.Product.objects.filter(
            company=company_id,
            category__is_active=True,
            is_active=True,
        )
        if branch_id:
            if not is_valid_uuid(branch_id):
                return Response(
                    errors={"message": "provide a valid branch ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            branch = models.Branch.retrieve_company_branch(
                id=branch_id, company=company_id
            )
            if branch is None:
                return Response(
                    errors={"message": "branch not found."},
                    status_code=404,
                    status=status.HTTP_404_NOT_FOUND,
                )
            company_products = company_products.filter(selected_branches__id=branch_id)
        if search and is_valid_string(search):
            company_products = company_products.filter(
                Q(name__icontains=search) | Q(category__name__icontains=search)
            )
        if category_id:
            if not is_valid_uuid(category_id):
                return Response(
                    errors={"message": "provide a valid category ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            company_products = company_products.filter(category=category_id)
        if subcategory_id:
            if not is_valid_uuid(subcategory_id):
                return Response(
                    errors={"message": "provide a valid subcategory ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            company_products = company_products.filter(subcategory=subcategory_id)
        if product_id:
            if not is_valid_uuid(product_id):
                return Response(
                    errors={"message": "provide a valid product ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            company_products = company_products.filter(id=product_id)
        paginated_response = Paginator.paginate(
            request=request, queryset=company_products
        )
        serializer = self.serializer_class(instance=paginated_response, many=True)
        data = {
            "message": "successfully fetched company's products.",
            "products": serializer.data,
            "count": len(serializer.data),
            "total_products": company_products.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)

    def delete(self, request, *args, **kwargs):
        serializer = self.delete_serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        destroy = utils.destroy_company_product(**serializer.validated_data)
        return Response(
            data={"message": destroy.get("message")},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class SupplierAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]
    serializer_class = serializers.SupplierSerializer
    update_serializer = serializers.SupplierUpdateSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(created_by=request.user)
        data = {
            "message": "successfully created user's company supplier.",
            "supplier": serializer.data,
        }
        return Response(data=data, status_code=201, status=status.HTTP_201_CREATED)

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        supplier_id = request.query_params.get("supplier")
        search = request.query_params.get("search")
        suppliers = models.Supplier.objects.filter(company=company)
        if search:
            suppliers = suppliers.filter(
                Q(name__icontains=search)
                | Q(email__icontains=search)
                | Q(phone_number__icontains=search)
            )
        if supplier_id:
            if (
                not is_valid_uuid(supplier_id)
                or not suppliers.filter(id=supplier_id).exists()
            ):
                return Response(
                    errors={"message": "provide a valid supplier ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            suppliers = suppliers.filter(id=supplier_id)
        paginated_response = Paginator.paginate(request=request, queryset=suppliers)
        serializer = self.serializer_class(instance=paginated_response, many=True)
        data = {
            "message": "successfully fetched suppliers.",
            "suppliers": serializer.data,
            "count": len(serializer.data),
            "total_suppliers": suppliers.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)

    def patch(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        supplier_id = request.query_params.get("supplier")
        data = request.data
        data["updated_by"] = request.user.id
        if supplier_id is None or not is_valid_uuid(supplier_id):
            return Response(
                errors={"message": "provide a valid supplier ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        supplier = models.Supplier.objects.filter(
            company=company, id=supplier_id
        ).first()
        if supplier is None:
            return Response(
                errors={"message": "provide a valid supplier ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = self.update_serializer(supplier, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(
            data=serializer.data, status_code=200, status=status.HTTP_200_OK
        )

    def delete(self, request, *args, **kwargs):
        serializer = self.update_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        supplier = models.Supplier.destroy(
            user=request.user, **serializer.validated_data
        )
        if supplier is None:
            return Response(
                data={"message": "supplier not found."},
                status_code=404,
                status=status.HTTP_404_NOT_FOUND,
            )
        return Response(
            data=supplier, status_code=204, status=status.HTTP_204_NO_CONTENT
        )


class StockManualUploadAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def put(self, request, *args, **kwargs):
        serializer = serializers.StockManualUploadSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        upload_response = models.StockDetail.upload_stock_details(
            user=request.user, **serializer.validated_data
        )
        if upload_response is None:
            return Response(
                errors={"message": "Provide a valid product ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        data = {
            "message": "stock upload was processed successfully.",
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchStockAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        search = request.query_params.get("search")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        stocks = models.StockDetail.objects.filter(
            company=company_id, branch=branch_id
        ).order_by("-updated_at")
        if search and is_valid_string(search):
            stocks = stocks.filter(
                Q(category__name__icontains=search) | Q(item__name__icontains=search)
            )
        if result_type and is_valid_string((result_type)):
            stocks = QuerysetCustomFilter.date_range_filter(
                queryset=stocks,
                result_type=result_type,
                start_date=start_date,
                end_date=end_date,
            )
            if not isinstance(stocks, QuerySet):
                return Response(
                    errors=stocks,
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
        paginated_response = Paginator.paginate(request=request, queryset=stocks)
        serializer = serializers.BranchStockDetailSerializer(
            instance=paginated_response, many=True
        )
        data = {
            "message": "successfully fetched branch stock details.",
            "stock_details": serializer.data,
            "count": len(serializer.data),
            "total_stocks": stocks.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchInventorySummaryAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        # Today's stock details / closing stock.
        current_stock_details = (
            models.StockDetail.objects.filter(
                company=company_id,
                branch=branch_id,
            ).aggregate(total_stock=Sum("quantity"))["total_stock"]
            or 0
        )
        inventory_data = utils.branch_opening_stock(branch=branch_id)
        # Today sales transaction(s).
        current_sales_details = (
            SalesTransactionItem.objects.filter(
                company=company_id,
                branch=branch_id,
                sales_transaction__status=enums.TransactionStatusChoices.SUCCESSFUL,
                created_at__date=TODAY.date(),
            ).aggregate(total_stock=Sum("quantity"))["total_stock"]
            or 0
        )
        # Todays's purchase order(s).
        current_purchase_details = (
            models.PurchaseOrder.objects.filter(
                company=company_id,
                branch=branch_id,
                created_at__date=TODAY.date(),
            ).aggregate(total_stock=Sum("quantity_requested"))["total_stock"]
            or 0
        )
        data = {
            "message": "successfully fetched branch record.",
            "inventory_status": {
                "opening_stock": {
                    "stock_count": inventory_data.get("total_stock"),
                    "date": inventory_data.get("date"),
                    "time": inventory_data.get("time"),
                },
                "closing_stock": {
                    "stock_count": current_stock_details,
                    "date": TODAY.date(),
                    "time": TODAY.time(),
                },
            },
            "inventory_journal": {
                "sales": {
                    "stock_count": current_sales_details,
                    "date": TODAY.date(),
                    "time": TODAY.time(),
                },
                "purchase": {
                    "stock_count": current_purchase_details,
                    "date": TODAY.date(),
                    "time": TODAY.time(),
                },
            },
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class StockVariantAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.VariantDetailSerializer

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")
        category_id = request.query_params.get("category")
        product_id = request.query_params.get("item")
        variant_id = request.query_params.get("variant")
        if not category_id or not product_id:
            return Response(
                errors={
                    "message": "provide a valid category and item IDs to view variant(s)."
                },
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not is_valid_uuid(category_id):
            return Response(
                errors={"message": "provide a valid category ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not is_valid_uuid(product_id):
            return Response(
                errors={"message": "provide a valid product ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        branch_stock_variants = models.StockVariant.objects.filter(
            company=company,
            branch=branch,
            stock_item__category=category_id,
            stock_item__item=product_id,
        )
        if branch_stock_variants.exists():
            if variant_id:
                if not is_valid_uuid(variant_id):
                    return Response(
                        errors={"message": "provide a valid variant ID."},
                        status_code=400,
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                branch_stock_variant = branch_stock_variants.filter(
                    id=variant_id
                ).first()
                if branch_stock_variant is not None:
                    serializer = self.serializer_class(instance=branch_stock_variant)
                    data = {
                        "message": "successfully fetched stock variant.",
                        "variants": serializer.data,
                    }
                    return Response(
                        data=data, status_code=200, status=status.HTTP_200_OK
                    )
                else:
                    data = {"message": "variant not found.", "variant": []}
                    return Response(
                        data=data, status_code=404, status=status.HTTP_200_OK
                    )

            paginated_response = Paginator.paginate(
                request=request, queryset=branch_stock_variants
            )
            serializer = self.serializer_class(instance=paginated_response, many=True)
            data = {
                "message": "successfully fetched stock variants.",
                "variants": serializer.data,
                "count": len(serializer.data),
                "total_variants": len(branch_stock_variants),
            }
        else:
            data = {
                "message": "stock item has no variants.",
                "variants": [],
                "count": 0,
                "total_variants": 0,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class SupplierHistoryAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        supplier_id = request.query_params.get("supplier")

        if supplier_id:
            if (
                not is_valid_uuid(supplier_id)
                or not models.Supplier.objects.filter(id=supplier_id).exists()
            ):
                return Response(
                    errors={"message": "provide a valid supplier ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            supplier = models.Supplier.objects.filter(id=supplier_id).first()
            supplier_history = models.SupplierHistory.objects.filter(
                company=company, supplier=supplier
            ).prefetch_related("supply_variants")
            if supplier_history.exists():
                categories_supplied = supplier_history.values("category").distinct()
                products_supplied = supplier_history.aggregate(Sum("quantity"))
                paginated_response = Paginator.paginate(
                    request=request, queryset=supplier_history
                )
                serializer = serializers.SupplierHistoryDetailsSerializer(
                    instance=paginated_response, many=True
                )
                data = {
                    "message": "successfully fetched company's supplier history details.",
                    "supplier": {
                        "name": supplier.name,
                        "email": supplier.email,
                        "phone_number": supplier.phone_number,
                        "categories_supplied": categories_supplied.count(),
                        "products_supplied": products_supplied["quantity__sum"],
                    },
                    "supply_history": serializer.data,
                    "count": len(serializer.data),
                    "total_history": supplier_history.count(),
                }
            else:
                data = {
                    "message": "supplier has no history.",
                    "supplier": {
                        "name": supplier.name,
                        "email": supplier.email,
                        "phone_number": supplier.phone_number,
                        "categories_supplied": 0,
                        "products_supplied": 0,
                    },
                    "supply_history": [],
                    "count": 0,
                    "total_history": supplier_history.count(),
                }
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)

        supplier_histories = models.SupplierHistory.objects.filter(company=company)
        if supplier_histories.exists():
            paginated_response = Paginator.paginate(
                request=request, queryset=supplier_histories
            )
            serializer = serializers.SupplierHistorySummarySerializer(
                instance=paginated_response, many=True
            )
            data = {
                "message": "successfully fetched company's supplier histories details.",
                "supply_histories": serializer.data,
                "count": len(serializer.data),
                "total_histories": supplier_histories.count(),
            }
        else:
            data = {
                "message": "company has no supplier histories details yet.",
                "supply_histories": [],
                "count": 0,
                "total_histories": 0,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class SupplierRatingAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        supplier_performances = models.SupplierHistory.suppliers_performance_rating(
            company=company_id
        )
        if supplier_performances is None:
            data = {
                "message": "suppliers' rating not available yet.",
                "top_5": [],
                "bottom_5": [],
            }
        else:
            data = {
                "message": "successfully fetched suppliers' rating.",
                "top_5": supplier_performances[:5],
                "bottom_5": supplier_performances[-5:],
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class UniqueIdAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.StockUniqueIdSerializer
    create_serializer_class = serializers.StockUniqueIdCreateSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.create_serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        unique_ids = models.StockUniqueId.upload_unique_ids(
            user=request.user, **serializer.validated_data
        )
        if unique_ids is False:
            return Response(
                errors={
                    "message": "invalid file format (expected column 'UNIQUE ID'), try again."
                },
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        data = {"message": "unique ID upload was successful."}
        return Response(data=data, status_code=201, status=status.HTTP_201_CREATED)

    def get(self, request, *args, **kwargs):
        branch_id = request.query_params.get("branch")
        stock_item_id = request.query_params.get("stock_item")
        stock_variant_id = request.query_params.get("stock_variant")
        search_id = request.query_params.get("search_id")

        if not stock_item_id and not stock_variant_id:
            return Response(
                errors={"message": "provide a valid stock item or variant IDs."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if stock_item_id:
            if not is_valid_uuid(stock_item_id):
                return Response(
                    errors={"message": "provide a valid stock item ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            item_unique_ids = models.StockUniqueId.objects.filter(
                branch=branch_id, stock_item=stock_item_id
            )
            if search_id and is_valid_string(search_id):
                item_unique_ids = item_unique_ids.filter(unique_id__icontains=search_id)
            paginated_response = Paginator.paginate(
                request=request, queryset=item_unique_ids
            )
            serializer = self.serializer_class(instance=paginated_response, many=True)
            data = {
                "message": "successfully fetched unique IDs.",
                "unique_ids": serializer.data,
                "count": len(serializer.data),
                "total_unique_ids": len(item_unique_ids),
            }
        if stock_variant_id:
            if not is_valid_uuid(stock_variant_id):
                return Response(
                    errors={"message": "provide a valid stock variant ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            variant_unique_ids = models.StockUniqueId.objects.filter(
                branch=branch_id, stock_variant=stock_variant_id
            )
            if search_id and is_valid_string(search_id):
                variant_unique_ids = variant_unique_ids.filter(
                    unique_id__icontains=search_id
                )
            paginated_response = Paginator.paginate(
                request=request, queryset=variant_unique_ids
            )
            serializer = self.serializer_class(instance=paginated_response, many=True)
            data = {
                "message": "successfully fetched unique IDs.",
                "unique_ids": serializer.data,
                "count": len(serializer.data),
                "total_unique_ids": len(variant_unique_ids),
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class PriceListAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]
    serializer_class = serializers.PriceListDetailsSerializer
    create_serializer_class = serializers.PriceListCreateSerializer
    update_serializer_class = serializers.PriceListUpdateSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.create_serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        price_lists = models.PriceList.create_company_price_list(
            user=request.user, **serializer.validated_data
        )
        serializer = self.serializer_class(instance=price_lists, many=True)
        data = {
            "message": "successfully added company's price list.",
            "price_lists": serializer.data,
        }
        return Response(data=data, status_code=201, status=status.HTTP_201_CREATED)

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        price_list_id = request.query_params.get("price_list")
        search = request.query_params.get("search")

        price_lists = models.PriceList.objects.filter(company=company)
        # This is used at the serializer level.
        branch = None
        if branch_id:
            if (
                not is_valid_uuid(branch_id)
                or models.Branch.retrieve_company_branch(id=branch_id, company=company)
                is None
            ):
                return Response(
                    errors={"message": "provide a valid branch ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            price_lists = price_lists.filter(
                Q(all_branches=True) | Q(selected_branches__id=branch_id)
            )
        if search and is_valid_string(search):
            price_lists = price_lists.filter(
                Q(category__name__icontains=search) | Q(item__name__icontains=search)
            )
        if price_list_id:
            if (
                not is_valid_uuid(price_list_id)
                or not price_lists.filter(id=price_list_id).exists()
            ):
                return Response(
                    errors={"message": "provide a valid price list ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            price_lists = price_lists.filter(id=price_list_id)
        paginated_response = Paginator.paginate(request=request, queryset=price_lists)
        serializer = self.serializer_class(
            instance=paginated_response, many=True, context={"branch": branch}
        )
        data = {
            "message": "successfully fetched price list details.",
            "price_lists": serializer.data,
            "count": len(serializer.data),
            "total_price_lists": price_lists.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)

    def patch(self, request, *args, **kwargs):
        serializer = self.update_serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        price_list = models.PriceList.update_price_list(
            user=request.user, **serializer.validated_data
        )
        serializer = self.serializer_class(instance=price_list)
        data = {
            "message": "price list updated successfully.",
            "price_list": serializer.data,
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class StockOutAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.StockOutSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(requested_by=request.user)
        return Response(
            data=serializer.data, status_code=201, status=status.HTTP_201_CREATED
        )

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        request_id = request.query_params.get("request")
        search = request.query_params.get("search")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        stockout_request = models.StockOut.objects.filter(
            company=company_id, branch=branch_id
        )
        if search and is_valid_string(search):
            stockout_request = stockout_request.filter(
                Q(category__name__icontains=search)
                | Q(item__name__icontains=search)
                | Q(requested_by__first_name__icontains=search)
                | Q(status__icontains=search)
            )
        if request_id and is_valid_string(request_id):
            if not is_valid_uuid(request_id):
                return Response(
                    errors={"message": "provide a valid request ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            stockout_request = stockout_request.filter(id=request_id)
        if result_type and is_valid_string((result_type)):
            stockout_request = QuerysetCustomFilter.date_range_filter(
                queryset=stockout_request,
                result_type=result_type,
                start_date=start_date,
                end_date=end_date,
            )
            if not isinstance(stockout_request, QuerySet):
                return Response(
                    errors=stockout_request,
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
        paginated_response = Paginator.paginate(
            request=request, queryset=stockout_request
        )
        serializer = self.serializer_class(instance=paginated_response, many=True)
        data = {
            "message": "fetched branch's stock-out requests.",
            "stockout_requests": serializer.data,
            "count": len(serializer.data),
            "total_stockout_requests": stockout_request.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchStockOutDetailsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        company = Company.retrieve_company(id=company_id)
        branch = models.Branch.retrieve_company_branch(id=branch_id, company=company)
        branch_stock_out_requests = models.StockOut.objects.filter(
            company=company, branch=branch
        )
        branch_teams = Team.objects.filter(company=company, branch=branch)
        branch_team_members = (
            TeamMember.objects.filter(team__in=branch_teams, is_active=True)
            .order_by("member_id")
            .distinct("member")
        )
        total_requests = branch_stock_out_requests.count()
        approved = branch_stock_out_requests.filter(status="APPROVED").count()
        pending = branch_stock_out_requests.filter(status="PENDING").count()
        declined = branch_stock_out_requests.filter(status="DECLINED").count()
        data = {
            "company": company.company_name,
            "branch_location": branch.name,
            "members": branch_team_members.count(),
            "request": total_requests,
            "approved": approved,
            "pending": pending,
            "declined": declined,
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchStockOutActionAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def put(self, request, *args, **kwargs):
        serializer = serializers.StockOutActionSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        stockout_request = models.StockOut.approve_decline_request(
            user=request.user, **serializer.validated_data
        )
        if not stockout_request.get("status"):
            return Response(
                data={"message": stockout_request.get("message")},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if stockout_request.get("status"):
            serializer = serializers.StockOutSerializer(
                instance=stockout_request.get("message")
            )
            data = {"message": "success", "request": serializer.data}
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchCategoryAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        category_id = request.query_params.get("category")

        if category_id:
            if not is_valid_uuid(category_id):
                return Response(
                    errors={"message": "provide a valid category ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if models.Category.objects.filter(id=category_id).last() is None:
                return Response(
                    errors={"message": "provide a valid category ID."},
                    status_code=404,
                    status=status.HTTP_404,
                )
            category_stock_details = models.StockDetail.objects.filter(
                company=company_id, branch=branch_id, category=category_id
            ).aggregate(
                date=Min("created_at"),
                total_stock=Sum("quantity"),
                total_stock_value=Sum("stock_value"),
            )
            date_time = category_stock_details.get("date")
            data = {
                "message": "success",
                "total_stock": {
                    "stock_count": category_stock_details.get("total_stock") or 0,
                    "last_added_date": (
                        date_time.date() if date_time is not None else ""
                    ),
                    "last_added_time": (
                        date_time.time() if date_time is not None else ""
                    ),
                },
                "stock_value": category_stock_details.get("total_stock_value") or 0,
            }
        else:
            stock_details = models.StockDetail.objects.filter(
                company=company_id, branch=branch_id
            ).aggregate(
                date=Min("created_at"),
                total_stock=Sum("quantity"),
                total_stock_value=Sum("stock_value"),
            )
            date_time = stock_details.get("date")
            data = {
                "message": "success",
                "total_stock": {
                    "stock_count": stock_details.get("total_stock") or 0,
                    "last_added_date": (
                        date_time.date() if date_time is not None else ""
                    ),
                    "last_added_time": (
                        date_time.time() if date_time is not None else ""
                    ),
                },
                "stock_value": stock_details.get("total_stock_value") or 0,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class StockFileUploadAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def put(self, request, *args, **kwargs):
        serializer = serializers.StockFileUploadSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        stock_upload = models.StockDetail.upload_stock_file(
            user=request.user, **serializer.validated_data
        )
        if not stock_upload:
            return Response(
                errors={"message": "invalid column header(s), try again."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        data = {
            "message": "stock upload was successful.",
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class StockSampleSheetAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        file_path = os.path.join(settings.BASE_DIR, "media/documents")
        return FileResponse(
            open(f"{file_path}/new_stock_upload.xlsx", "rb"), as_attachment=True
        )


class BranchProductOverviewAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.BranchProductSerializer

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        search = request.query_params.get("search")
        # Branch is used for context.
        branch = models.Branch.retrieve_company_branch(id=branch_id, company=company_id)
        company_products = models.Product.objects.filter(
            company=company_id,
            selected_branches__id=branch_id,
        )
        if search and is_valid_string(search):
            company_products = company_products.filter(
                Q(category__name__icontains=search) | Q(name__icontains=search)
            )
        paginated_response = Paginator.paginate(
            request=request, queryset=company_products
        )
        serializer = self.serializer_class(
            instance=paginated_response, many=True, context={"branch": branch}
        )
        data = {
            "message": "successfully fetched branch's products.",
            "products": serializer.data,
            "count": len(serializer.data),
            "total_products": company_products.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class StockRequestAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]
    create_serializer_class = serializers.StockRequestCreateSerializer
    summary_serializer_class = serializers.StockRequestSummarySerializer

    def post(self, request, *args, **kwargs):
        serializer = self.create_serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        stock_requests = models.StockRequest.register_request(
            requested_by=request.user, **serializer.validated_data
        )
        data = {"message": "stock request was successful.", "requests": stock_requests}
        return Response(data=data, status_code=201, status=status.HTTP_201_CREATED)

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        request_id = request.query_params.get("request")
        search = request.query_params.get("search")

        if branch_id is None or not is_valid_uuid(branch_id):
            return Response(
                errors={"message": "provide a valid branch ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        branch = models.Branch.retrieve_company_branch(id=branch_id, company=company_id)
        branch_stock_requests = models.StockRequest.objects.filter(
            Q(demand_branch=branch) | Q(supply_branch=branch)
        )
        if branch_stock_requests.exists():
            aggregated_data = (
                branch_stock_requests.values("request_id")
                .annotate(
                    date=Min("created_at"),
                    requested_by=F("requested_by__first_name"),
                    category=Count("category", distinct=True),
                    item=Count("item", distinct=True),
                    quantity=Sum("quantity_requested"),
                    request_time=Min("created_at"),
                    request_reason=F("request_reason"),
                    demand_branch=F("demand_branch__name"),
                    supply_branch=F("supply_branch__name"),
                    status=F("status"),
                    approved_by=F("approved_by__first_name"),
                    date_approved=Min("date_approved"),
                )
                .order_by("-date")
            )
            if search and is_valid_string(search):
                aggregated_data = aggregated_data.filter(
                    Q(demand_branch__icontains=search)
                    | Q(supply_branch__icontains=search)
                    | Q(requested_by__icontains=search)
                    | Q(status__icontains=search)
                )

            if request_id and is_valid_string(request_id):
                aggregated_data = aggregated_data.filter(request_id=request_id)
            paginated_response = Paginator.paginate(
                request=request, queryset=aggregated_data
            )
            serializer = self.summary_serializer_class(
                instance=paginated_response, many=True, context={"branch": branch}
            )
            data = {
                "message": "branch's stock request(s).",
                "requests": serializer.data,
                "count": len(serializer.data),
                "total_requests": aggregated_data.count(),
            }
        else:
            data = {
                "message": "stock request(s) not available yet.",
                "requests": [],
                "count": 0,
                "total_requests": 0,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchStockRequestDetailsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        company = Company.retrieve_company(id=company_id)
        branch = models.Branch.retrieve_company_branch(id=branch_id, company=company)
        branch_stock_requests = models.StockRequest.objects.filter(
            Q(demand_branch=branch) | Q(supply_branch=branch)
        )
        branch_teams = Team.objects.filter(company=company, branch=branch).values_list(
            "id", flat=True
        )
        branch_team_members = (
            TeamMember.objects.filter(team__in=branch_teams, is_active=True)
            .order_by("member_id")
            .distinct("member")
        )
        if branch_stock_requests.exists():
            aggregated_data = branch_stock_requests.values("request_id").annotate(
                category=Count("category", distinct=True),
                item=Count("item", distinct=True),
                quantity=Sum("quantity_requested"),
                demand_branch=F("demand_branch__name"),
                supply_branch=F("supply_branch__name"),
                requested_by=F("requested_by__first_name"),
                status=F("status"),
                date=Min("created_at"),
            )
            total_requests = aggregated_data.count()
            approved = aggregated_data.filter(status="APPROVED").count()
            pending = aggregated_data.filter(status="PENDING").count()
            declined = aggregated_data.filter(status="DECLINED").count()
            data = {
                "company": company.company_name,
                "branch_location": branch.name,
                "members": branch_team_members.count(),
                "requests": total_requests,
                "approved": approved,
                "pending": pending,
                "declined": declined,
            }
        else:
            data = {
                "company": company.company_name,
                "branch_location": branch.name,
                "members": branch_team_members.count(),
                "requests": 0,
                "approved": 0,
                "pending": 0,
                "declined": 0,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class RequestDetailsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.StockRequestSerializer

    def get(self, request, *args, **kwargs):
        branch = request.query_params.get("branch")
        category_id = request.query_params.get("category")
        request_id = request.query_params.get("request")
        search = request.query_params.get("search")

        if not request_id:
            return Response(
                errors={"message": "provide a valid request ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        stock_requests_details = models.StockRequest.objects.filter(
            request_id=request_id
        ).filter(Q(demand_branch=branch) | Q(supply_branch=branch))
        if search and is_valid_string(search):
            stock_requests_details = stock_requests_details.filter(
                Q(category__name__icontains=search)
                | Q(item__name__icontains=search)
                | Q(status__icontains=search)
            )
        if category_id:
            if not is_valid_uuid(category_id):
                return Response(
                    errors={"message": "provide a valid category ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if models.Category.objects.filter(id=category_id).first() is None:
                return Response(
                    errors={"message": "provide a valid category ID."},
                    status_code=404,
                    status=status.HTTP_400,
                )
            stock_requests_details = stock_requests_details.filter(category=category_id)
        paginated_data = Paginator.paginate(
            request=request, queryset=stock_requests_details
        )
        serializer = self.serializer_class(
            instance=paginated_data, many=True, context={"branch": branch}
        )
        data = {
            "message": "success",
            "requests": serializer.data,
            "count": len(serializer.data),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class StockRequestActionAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = serializers.StockRequestActionHandlerSerializer

    def put(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        stock_request = models.StockRequest.approve_decline_request(
            user=request.user, **serializer.validated_data
        )
        if stock_request.get("status") is None:
            return Response(
                data={"message": stock_request.get("message")},
                status_code=404,
                status=status.HTTP_200_OK,
            )
        if stock_request.get("status") is False:
            return Response(
                errors={"message": stock_request.get("message")},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if stock_request.get("status") is True:
            serializer = serializers.StockRequestSerializer(
                instance=stock_request.get("data"), many=True
            )
            data = {"message": "success", "request": serializer.data}
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchInventoryStatusSummaryAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    summary_serializer_class = serializers.BranchInventoryStatusSummarySerializer

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")
        branch_inventory_status = models.InventoryStatus.objects.filter(
            company=company, branch=branch
        )
        if branch_inventory_status.exists():
            aggregated_data = (
                branch_inventory_status.annotate(date=TruncDate("created_at"))
                .values("date")
                .annotate(
                    opening_stock=Sum("opening_quantity"),
                    opening_value=Sum("opening_value"),
                    closing_stock=Sum("closing_quantity"),
                    closing_value=Sum("closing_value"),
                    branch=F("branch__name"),
                    company=F("company__company_name"),
                )
                .order_by("-date")
            )
            paginated_response = Paginator.paginate(
                request=request, queryset=aggregated_data
            )
            serializer = self.summary_serializer_class(
                instance=paginated_response, many=True
            )
            data = {
                "message": "successfully fetched branch's inventory status summary.",
                "inventory_status": serializer.data,
                "count": len(serializer.data),
                "total_inventory_status": aggregated_data.count(),
            }
        else:
            data = {
                "message": "branch has no inventory status yet.",
                "inventory_status": [],
                "count": 0,
                "total_inventory_status": 0,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchInventoryStatusAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.InventoryStatusSerializer

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")
        date = request.query_params.get("date")
        if not date:
            return Response(
                errors={"message": "provide a valid a date (YYYY-MM-DD)."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if format_string_to_date_object(date) is None:
            return Response(
                errors={"message": "invalid date format (YYYY-MM-DD)."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        branch_inventory_status = models.InventoryStatus.objects.filter(
            company=company,
            branch=branch,
            created_at__date=date,
        )
        paginated_response = Paginator.paginate(
            request=request, queryset=branch_inventory_status
        )
        serializer = self.serializer_class(instance=paginated_response, many=True)
        data = {
            "message": "successfully fetched branch's inventory status.",
            "inventory_status": serializer.data,
            "count": len(serializer.data),
            "total_inventory_status": branch_inventory_status.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchStockHistoryAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]
    serializer_class = serializers.BranchStockHistorySerializer

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        search = request.query_params.get("search")
        history_type = request.query_params.get("history_type")
        result_type = request.query_params.get("result_type")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        stock_histories = models.StockHistory.objects.filter(company=company_id)
        if branch_id:
            if (
                not is_valid_uuid(branch_id)
                or models.Branch.retrieve_company_branch(
                    id=branch_id, company=company_id
                )
                is None
            ):
                return Response(
                    errors={"message": "provide a valid branch ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            stock_histories = stock_histories.filter(branch=branch_id)
        if search and is_valid_string(search):
            stock_histories = stock_histories.filter(
                Q(category__name__icontains=search)
                | Q(item__name__icontains=search)
                | Q(supplier__name__icontains=search)
            )
        if history_type and is_valid_string(history_type):
            if history_type.upper() not in [
                history[0] for history in stock_enums.StockHistoryChoices.choices
            ]:
                return Response(
                    errors={"message": "provide a valid stock history type."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            stock_histories = stock_histories.filter(
                transaction_type=history_type.upper()
            )
        if result_type and is_valid_string((result_type)):
            stock_histories = QuerysetCustomFilter.date_range_filter(
                queryset=stock_histories,
                result_type=result_type,
                start_date=start_date,
                end_date=end_date,
            )
            if not isinstance(stock_histories, QuerySet):
                return Response(
                    errors=stock_histories,
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
        paginated_response = Paginator.paginate(
            request=request, queryset=stock_histories
        )
        serializer = self.serializer_class(instance=paginated_response, many=True)
        data = {
            "message": "stock histories.",
            "stock_histories": serializer.data,
            "count": len(serializer.data),
            "total_stock_histories": stock_histories.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchStockRequestTransitAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def put(self, request, *args, **kwargs):
        serializer = serializers.BranchStockRequestTransitSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        stock_request = models.StockRequest.transit_request(
            user=request.user, **serializer.validated_data
        )
        if stock_request.get("status") is None:
            return Response(
                data={"message": stock_request.get("message")},
                status_code=404,
                status=status.HTTP_200_OK,
            )
        if stock_request.get("status") is False:
            return Response(
                errors={"message": stock_request.get("message")},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if stock_request.get("status") is True:
            serializer = serializers.StockRequestSerializer(
                instance=stock_request.get("data"), many=True
            )
            data = {
                "message": "success",
                "request": serializer.data,
                "total_requests": len(serializer.data),
            }
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class BranchStockRequestReceiveAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def put(self, request, *args, **kwargs):
        serializer = serializers.BranchStockRequestReceiveSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        stock_request = models.StockRequest.receive_request(
            user=request.user, **serializer.validated_data
        )
        if stock_request.get("status") is None:
            return Response(
                data={"message": stock_request.get("message")},
                status_code=404,
                status=status.HTTP_200_OK,
            )
        if stock_request.get("status") is False:
            return Response(
                errors={"message": stock_request.get("message")},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if stock_request.get("status") is True:
            serializer = serializers.StockRequestSerializer(
                instance=stock_request.get("data"), many=True
            )
            data = {
                "message": "success",
                "request": serializer.data,
                "total_requests": len(serializer.data),
            }
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class PurchaseOrderAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.PurchaseOrderSerializer
    create_serializer_class = serializers.PurchaseOrderCreateSerializer
    summary_serializer_class = serializers.PurchaseOrderSummarySerializer

    def post(self, request, *args, **kwargs):
        serializer = self.create_serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        orders = models.PurchaseOrder.register_order(
            user=request.user, **serializer.validated_data
        )
        data = {"message": "successfully created purchase order", "orders": orders}
        return Response(data=data, status_code=201, status=status.HTTP_201_CREATED)

    def get(self, request, *args, **kwargs):
        branch_id = request.query_params.get("branch")
        search = request.query_params.get("search")
        branch_purchase_orders = models.PurchaseOrder.objects.filter(branch=branch_id)
        if branch_purchase_orders.exists():
            aggregated_data = (
                branch_purchase_orders.values("request_id")
                .annotate(
                    branch=F("branch__name"),
                    date_issued=Min("created_at"),
                    supplier=F("supplier__name"),
                    category=Count("category", distinct=True),
                    item=Count("item", distinct=True),
                    quantity=Sum("quantity_requested"),
                    requested_by=F("requested_by__first_name"),
                    status=F("status"),
                    date_delivered=Min("date_delivered"),
                )
                .order_by("-date_issued")
            )
            if search and is_valid_string(search):
                aggregated_data = aggregated_data.filter(
                    Q(supplier__icontains=search)
                    | Q(status__icontains=search)
                    | Q(request_id__icontains=search)
                )
            paginated_response = Paginator.paginate(
                request=request, queryset=aggregated_data
            )
            serializer = self.summary_serializer_class(
                instance=paginated_response, many=True
            )
            data = {
                "message": "fetched branch's purchase order(s).",
                "orders": serializer.data,
                "count": len(serializer.data),
                "total_orders": aggregated_data.count(),
            }
        else:
            data = {
                "message": "branch has no purchase order(s) yet.",
                "orders": [],
                "count": 0,
                "total_orders": 0,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class PurchaseOrderDetailsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.PurchaseOrderSerializer

    def get(self, request, *args, **kwargs):
        branch_id = request.query_params.get("branch")
        request_id = request.query_params.get("request")
        search = request.query_params.get("search")
        if not request_id:
            return Response(
                errors={"message": "provide a valid request ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        purchase_order_details = models.PurchaseOrder.objects.filter(
            branch=branch_id, request_id=request_id
        )
        if search and is_valid_string(search):
            purchase_order_details = purchase_order_details.filter(
                Q(category__name__icontains=search)
                | Q(item__name__icontains=search)
                | Q(supplier__name__icontains=search)
                | Q(status__icontains=search)
            )
        serializer = self.serializer_class(instance=purchase_order_details, many=True)
        data = {
            "message": "success",
            "orders": serializer.data,
            "count": len(serializer.data),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class PurchaseOrderSummaryAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        branch_id = request.query_params.get("branch")
        request_id = request.query_params.get("request")

        if not request_id:
            return Response(
                errors={"message": "provide a valid request ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        branch_purchase_orders = models.PurchaseOrder.objects.filter(
            branch=branch_id, request_id=request_id
        )
        if branch_purchase_orders.exists():
            aggregated_data = (
                branch_purchase_orders.values("request_id")
                .annotate(
                    branch=F("branch__name"),
                    date_issued=Min("created_at"),
                    supplier=F("supplier__name"),
                    category=Count("category", distinct=True),
                    item=Count("item", distinct=True),
                    quantity=Sum("quantity_requested"),
                    requested_by=F("requested_by__first_name"),
                    status=F("status"),
                    date_delivered=Min("date_delivered"),
                )
                .order_by("-date_issued")
            )
            request_instance = aggregated_data.first()
            data = {
                "supplier": request_instance.get("supplier"),
                "category": request_instance.get("category"),
                "item": request_instance.get("item"),
                "quantity": request_instance.get("quantity"),
                "status": request_instance.get("status"),
            }
        else:
            data = {
                "supplier": None,
                "category": None,
                "item": None,
                "quantity": None,
                "status": None,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class StockTransferAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]
    serializer_class = serializers.StockTransferSerializer
    create_serializer_class = serializers.StockTransferCreateSerializer
    summary_serializer_class = serializers.StockTransferSummarySerializer
    action_serializer_class = serializers.StockTransferActionSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.create_serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        transfers = models.StockTransfer.register_transfer(
            user=request.user, **serializer.validated_data
        )
        if not transfers.get("status"):
            return Response(
                errors={"message": transfers.get("message")},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if transfers.get("status"):
            data = {
                "message": "successfully created a transfer request.",
                "transfers": transfers.get("message"),
            }
            return Response(data=data, status_code=201, status=status.HTTP_201_CREATED)

    def get(self, request, *args, **kwargs):
        branch_id = request.query_params.get("branch")
        request_id = request.query_params.get("request")

        branch_transfer_requests = models.StockTransfer.objects.filter(branch=branch_id)
        if branch_transfer_requests.exists():
            aggregated_data = (
                branch_transfer_requests.values("request_id")
                .annotate(
                    date=Min("created_at"),
                    approved_by=F("approved_by__first_name"),
                    branch=F("transfer_to__name"),
                    category=Count("category", distinct=True),
                    item=Count("item", distinct=True),
                    quantity=Sum("quantity"),
                    status=F("status"),
                )
                .order_by("-date")
            )
            if request_id:
                request_aggregated_data = aggregated_data.filter(
                    request_id=request_id
                ).first()
                if request_aggregated_data is not None:
                    serializer = self.summary_serializer_class(
                        instance=request_aggregated_data
                    )
                    data = {
                        "message": "successfully fetched branch's transfer request.",
                        "transfers": serializer.data,
                    }
                    return Response(
                        data=data, status_code=200, status=status.HTTP_200_OK
                    )
                else:
                    data = {
                        "message": "transfer request not found.",
                        "transfers": [],
                    }
                    return Response(
                        data=data, status_code=404, status=status.HTTP_200_OK
                    )
            paginated_response = Paginator.paginate(
                request=request, queryset=aggregated_data
            )
            serializer = self.summary_serializer_class(
                instance=paginated_response, many=True
            )
            data = {
                "message": "successfully fetched branch's transfer requests.",
                "transfers": serializer.data,
                "count": len(serializer.data),
                "total_transfers": aggregated_data.count(),
            }
        else:
            data = {
                "message": "branch has no transfer requests yet.",
                "transfers": [],
                "count": 0,
                "total_transfers": 0,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)

    def put(self, request, *args, **kwargs):
        serializer = self.action_serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        stock_transfer = models.StockTransfer.transfer_transition(
            **serializer.validated_data
        )
        if stock_transfer.get("status") is None:
            return Response(
                data={"message": stock_transfer.get("message")},
                status_code=404,
                status=status.HTTP_200_OK,
            )
        if stock_transfer.get("status") is False:
            return Response(
                errors={"message": stock_transfer.get("message")},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if stock_transfer.get("status") is True:
            serializer = self.serializer_class(
                instance=stock_transfer.get("data"), many=True
            )
            data = {
                "message": "success",
                "request": serializer.data,
                "total_requests": len(serializer.data),
            }
            return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class StockTransferDetailsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]
    serializer_class = serializers.StockTransferSerializer

    def get(self, request, *args, **kwargs):
        branch_id = request.query_params.get("branch")
        request_id = request.query_params.get("request")

        if not request_id:
            return Response(
                errors={"message": "provide a valid request ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        branch_transfer_request_details = models.StockTransfer.objects.filter(
            branch=branch_id, request_id=request_id
        )
        if branch_transfer_request_details.exists():
            serializer = self.serializer_class(
                instance=branch_transfer_request_details, many=True
            )
            data = {
                "message": "success",
                "transfers": serializer.data,
                "count": len(serializer.data),
            }
        else:
            data = {"message": "success", "transfers": [], "count": 0}
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class StockTransferSummaryAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        request_id = request.query_params.get("request")

        if not request_id:
            return Response(
                errors={"message": "provide a valid request ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        branch_transfer_requests = models.StockTransfer.objects.filter(
            company=company_id, branch=branch_id, request_id=request_id
        )
        if branch_transfer_requests.exists():
            aggregated_data = (
                branch_transfer_requests.values("request_id")
                .annotate(
                    date=Min("created_at"),
                    supply_to=F("transfer_to__name"),
                    receiver=F("received_by__first_name"),
                    category=Count("category", distinct=True),
                    item=Count("item", distinct=True),
                    quantity=Sum("quantity"),
                    status=F("status"),
                )
                .order_by("-date")
            )
            request_instance = aggregated_data.first()
            data = {
                "date": request_instance.get("date"),
                "supply_to": request_instance.get("supply_to"),
                "receiver": request_instance.get("receiver"),
                "category": request_instance.get("category"),
                "item": request_instance.get("item"),
                "quantity": request_instance.get("quantity"),
                "status": request_instance.get("status"),
            }
        else:
            data = {
                "date": None,
                "supply_to": None,
                "receiver": None,
                "category": None,
                "item": None,
                "quantity": None,
                "status": None,
            }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class AvailableStockDetailsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        category_id = request.query_params.get("category_id")
        subcategory_id = request.query_params.get("subcategory_id")
        product_id = request.query_params.get("item_id")
        search = request.query_params.get("search")
        price_tag = request.query_params.get("price_tag")

        # Retrieve branch for the company
        branch = models.Branch.retrieve_company_branch(id=branch_id, company=company_id)
        # Filter the stock details for the company and branch
        stock_details = (
            models.PriceList.objects.filter(
                company=company_id,
                category__is_active=True,
                item__is_active=True,
                item__selected_branches__id=branch_id,
            )
            .order_by(
                "item__id",
                "-item__sales_ranking",
            )
            .distinct("item")
        )
        if search and is_valid_string(search):
            stock_details = stock_details.filter(
                Q(item__name__icontains=search)
                | Q(category__name__icontains=search)
                | Q(subcategory__name__icontains=search)
                | Q(item__sku__icontains=search)
            )
        if category_id and is_valid_uuid(category_id):
            stock_details = stock_details.filter(category=category_id)
        if subcategory_id and is_valid_uuid(subcategory_id):
            stock_details = stock_details.filter(item__subcategory=subcategory_id)
        if product_id and is_valid_uuid(product_id):
            stock_details = stock_details.filter(item=product_id)
        if price_tag and is_valid_string(price_tag):
            if not is_valid_uuid(price_tag):
                return Response(
                    errors={"message": "provide a valid price tag ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            price_tag = (
                models.PriceTag.objects.filter(
                    Q(system_default=True) | Q(company=company_id)
                )
                .filter(id=price_tag)
                .first()
            )
            if price_tag is None:
                return Response(
                    errors={"message": "provide a valid price tag ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
        # Pagination for the stock details
        paginated_data = Paginator.paginate(request=request, queryset=stock_details)
        serializer = serializers.AvailableStockSerializer(
            instance=paginated_data,
            many=True,
            context={
                "company": company_id,
                "branch": branch,
                "price_tag": price_tag,
            },
        )
        # Filtering StockDetail model based on the company and branch for additional data calculations
        stock_detail_filtered = models.StockDetail.objects.filter(
            company=company_id, branch=branch_id
        )
        # Calculating total products sold
        total_products_sold = (
            stock_detail_filtered.aggregate(total_sold=Sum("quantity"))["total_sold"]
            or 0
        )
        # Calculating total amount sold (quantity * selling price)
        total_amount_sold = (
            stock_detail_filtered.aggregate(
                total_revenue=Sum(F("quantity") * F("selling_price"))
            )["total_revenue"]
            or 0
        )
        # Calculating products in stock (having positive quantity)
        product_in_stock = stock_detail_filtered.filter(quantity__gt=0).count()
        # Calculating total inventory value
        inventory_value = (
            stock_detail_filtered.aggregate(total_value=Sum("stock_value"))[
                "total_value"
            ]
            or 0
        )
        # Calculating products out of stock (quantity = 0)
        product_out_stock = stock_detail_filtered.filter(quantity=0).count()

        # Added BNPL Products based on requirements (Structure of initial implementations is maintained)

        stock_data = serializer.data

        supplier_products_query = Q(supplier__company_id=company_id, is_active=True)
        if category_id:
            supplier_products_query &= Q(category_id=category_id)
        if subcategory_id:
            supplier_products_query &= Q(sub_category_id=subcategory_id)
        if product_id:
            supplier_products_query &= Q(id=product_id)
        if search:
            supplier_products_query &= (
                Q(name__icontains=search)
                | Q(category__name__icontains=search)
                | Q(sub_category__name__icontains=search)
            )

        supplier_products_data = models.SupplierProduct.objects.filter(
            supplier_products_query
        ).distinct()
        supplier_products = Paginator.paginate(
            request=request, queryset=supplier_products_data
        )

        supplier_product_map = list()
        for product in supplier_products:
            primary_image = models.SupplierProductImage.objects.filter(
                product=product, is_primary=True
            ).first()
            product_detail = (
                models.SupplierProductDetail.objects.filter(product=product)
                .order_by("-stock")
                .first()
            )

            similar_products = serializers.SimilarSupplierProductSerializerOut(
                models.SupplierProduct.objects.filter(
                    supplier__is_active=True,
                    is_active=True,
                    sub_category=product.sub_category,
                )
                .exclude(pk=product.id)
                .distinct()[:5],
                many=True,
                context={"request": request},
            ).data

            supplier_data = dict()
            if product.supplier:
                supplier = product.supplier
                supplier_data["id"] = supplier.id
                supplier_data["name"] = supplier.name
                supplier_data["email"] = supplier.email
                supplier_data["phone_number"] = supplier.phone_number
                supplier_data["address"] = supplier.get_full_address()

            product_data = {
                "category": product.category.name,
                "category_id": product.category_id,
                "item": product.name,
                "item_id": product.id,
                "description": product.description,
                "image": primary_image.image if primary_image else None,
                "product_vat": 0.0,
                "subcategory": product.sub_category.name,
                "subcategory_id": product.sub_category_id,
                "sku": product_detail.sku if product_detail else "",
                "price_tag": "selling_price",
                "selling_price": product_detail.price if product_detail else 0.0,
                "stock_price": product_detail.price if product_detail else 0.0,
                "quantity": product_detail.stock if product_detail else 0,
                "quantity_sold": product.sale_count,
                "is_bnpl": True,
                "supplier_detail": supplier_data,
                "similar_products": similar_products,
            }

            supplier_product_map.append(product_data)

        combined_results = stock_data
        if len(supplier_product_map) >= 1:
            combined_results = stock_data + supplier_product_map

        # Building response data
        data = {
            "message": "success",
            "stock": combined_results,
            "count": len(combined_results),
            "total_stock": stock_details.count(),
            "total_products_sold": total_products_sold,
            "total_amount_sold": total_amount_sold,
            "product_in_stock": product_in_stock,
            "inventory_value": inventory_value,
            "product_out_stock": product_out_stock,
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class ChatStockUploadAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def put(self, request, *args, **kwargs):
        serializer = serializers.ChatStockUploadSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        models.StockDetail.chat_upload_stock(
            user=request.user, **serializer.validated_data
        )
        return Response(
            data={"message": "stock upload was successful."},
            status_code=200,
            status=status.HTTP_200_OK,
        )


# class CreateStoreAPIView(APIView):
#     authentication_classes = [CustomUserAuthentication]
#     permission_classes = [IsAuthenticated]

#     def post(self, request):
#         serializer = serializers.CreateStoreSerializer(data=request.data)
#         serializer.is_valid(raise_exception=True)

#         company = serializer.validated_data.get("company")
#         description = serializer.validated_data.get("description")
#         store_url = serializer.validated_data.get("store_url")
#         brand_color = serializer.validated_data.get("brand_color")
#         store_logo = serializer.validated_data.get("store_logo")
#         company_store = models.CompanyStore.objects.filter(
#             company=company, is_deleted=False
#         ).first()
#         if company_store:
#             return Response(
#                 {"message": f"store exists for {company.company_name}"},
#                 status=status.HTTP_400_BAD_REQUEST,
#             )
#         if models.CompanyStore.objects.filter(store_url=store_url).exists():
#             return Response(
#                 {"message": f"{store_url} already exist"},
#                 status=status.HTTP_400_BAD_REQUEST,
#             )
#         company_store = models.CompanyStore.objects.create(
#             company=company,
#             store_description=description,
#             store_url=store_url,
#             store_brand_color=brand_color,
#             created_by=request.user,
#         )
#         upload_file_aws_s3_bucket(
#             model_instance_id=company_store.id,
#             file=store_logo,
#             model_name="CompanyWebStore",
#         )
#         return Response(
#             {"message": "store created successfully"}, status=status.HTTP_200_OK
#         )


# class ViewStoreDataAPIView(APIView):
#     authentication_classes = [CustomUserAuthentication]
#     permission_classes = [IsAuthenticated]

#     def get(self, request):
#         company_id = request.query_params.get("company_id")

#         company_store = models.CompanyStore.objects.filter(
#             company__id=company_id, is_deleted=False
#         ).first()
#         if not company_store:
#             return Response(
#                 {"message": "company store does not exist"},
#                 status=status.HTTP_400_BAD_REQUEST,
#             )
#         serializer = serializers.ViewCompanyStoreSerializer(company_store)
#         return Response(serializer.data, status=status.HTTP_200_OK)


# class EditStoreAPIView(APIView):
#     authentication_classes = [CustomUserAuthentication]
#     permission_classes = [IsAuthenticated]

#     def put(self, request):
#         company_id = request.query_params.get("company_id")

#         company_store = models.CompanyStore.objects.filter(
#             company__id=company_id, is_deleted=False
#         ).first()
#         if not company_store:
#             return Response(
#                 {"message": "company store does not exist"},
#                 status=status.HTTP_400_BAD_REQUEST,
#             )

#         serializer = serializers.EditStoreSerializer(
#             company_store, data=request.data, partial=True
#         )
#         serializer.is_valid(raise_exception=True)
#         serializer.save()
#         return Response({"message": "saved successfully"}, status=status.HTTP_200_OK)


# class EditStoreLogoAPIView(APIView):
#     authentication_classes = [CustomUserAuthentication]
#     permission_classes = [IsAuthenticated]

#     def put(self, request):
#         company_id = request.query_params.get("company_id")

#         company_store = models.CompanyStore.objects.filter(
#             company__id=company_id, is_deleted=False
#         ).first()
#         if not company_store:
#             return Response(
#                 {"message": "company store does not exist"},
#                 status=status.HTTP_400_BAD_REQUEST,
#             )

#         serializer = serializers.EditStoreLogoSerializer(data=request.data)
#         serializer.is_valid(raise_exception=True)
#         store_logo = serializer.validated_data.get("header_logo")
#         uploaded_file = upload_file_aws_s3_bucket(
#             model_instance_id=company_store.id,
#             file=store_logo,
#             model_name="CompanyWebStore",
#         )
#         response = {
#             "status": "Success",
#             "message": "Store logo updated successfully",
#             "file_url": uploaded_file if uploaded_file else "",
#         }
#         return Response(response, status=status.HTTP_200_OK)


# class EditStoreHeaderImageAPIView(APIView):
#     authentication_classes = [CustomUserAuthentication]
#     permission_classes = [IsAuthenticated]

#     def put(self, request):
#         company_id = request.query_params.get("company_id")

#         company_store = models.CompanyStore.objects.filter(
#             company__id=company_id, is_deleted=False
#         ).first()
#         if not company_store:
#             return Response(
#                 {"message": "company store does not exist"},
#                 status=status.HTTP_400_BAD_REQUEST,
#             )

#         serializer = serializers.EditStoreHeaderImageSerializer(data=request.data)
#         serializer.is_valid(raise_exception=True)
#         store_image = serializer.validated_data.get("header_image")
#         uploaded_file = upload_file_aws_s3_bucket(
#             model_instance_id=company_store.id,
#             file=store_image,
#             model_name="CompanyWebStoreHeaderImage",
#         )
#         response = {
#             "status": "Success",
#             "message": "header image updated successfully",
#             "file_url": uploaded_file if uploaded_file else "",
#         }
#         return Response(response, status=status.HTTP_200_OK)


class InstantWebProductAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def post(self, request):
        serializer = serializers.CreateProductSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        models.Product.create_product_and_variant(
            user=request.user, **serializer.validated_data
        )
        return Response(
            serializer.data, status_code=201, status=status.HTTP_201_CREATED
        )

    def patch(self, request, *args, **kwargs):
        company_id = request.query_params.get("company_id")
        branch_id = request.query_params.get("branch_id")
        product_id = request.query_params.get("item_id")

        # Ensure all required query parameters are present
        if not company_id or not branch_id or not product_id:
            return Response(
                {"message": "Missing required query parameters."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate if all IDs are provided
        if not company_id or not branch_id or not product_id:
            return Response(
                {
                    "message": "Missing required query parameters: company_id, branch_id, or item_id."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate UUID format
        if not is_valid_uuid(company_id):
            return Response(
                {"message": "Invalid UUID format for company ID."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not is_valid_uuid(branch_id):
            return Response(
                {"message": "Invalid UUID format for branch ID."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not is_valid_uuid(product_id):
            return Response(
                {"message": "Invalid UUID format for item ID."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate company_id
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                {"message": "Invalid company ID"}, status=status.HTTP_404_NOT_FOUND
            )

        # Validate branch_id
        try:
            branch = models.Branch.objects.get(id=branch_id)
        except models.Branch.DoesNotExist:
            return Response(
                {"message": "Invalid branch ID"}, status=status.HTTP_404_NOT_FOUND
            )

        # Validate product_id and ensure it belongs to the company
        try:
            product = models.Product.objects.get(id=product_id, company=company)
        except models.Product.DoesNotExist:
            return Response(
                {"message": "Invalid item ID"}, status=status.HTTP_404_NOT_FOUND
            )

        try:
            # Fetch the product using the item_id, and ensure the company matches
            product = models.Product.objects.get(id=product_id, company__id=company_id)
        except models.Product.DoesNotExist:
            return Response(
                {"message": "Product not found."}, status=status.HTTP_404_NOT_FOUND
            )

        # Perform partial update using the UpdateProductSerializer
        serializer = serializers.UpdateProductSerializer(
            product, data=request.data, partial=True
        )
        serializer.is_valid(raise_exception=True)

        # Save the updated product
        serializer.save()

        return Response(serializer.data, status=status.HTTP_200_OK)


class AIStockConverterAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def post(self, request, *args, **kwargs):
        company_id = request.data.get("company")
        branch_id = request.data.get("branch")
        is_upload = request.data.get("is_upload")
        is_snapshot = request.data.get("is_snapshot")

        if is_upload:
            image = request.FILES.get("image")
            if not image:
                return Response(
                    errors={"message": "no image provided for upload."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            ai_response = process_stock_items(
                image, is_base64=False, company_id=company_id, branch_id=branch_id
            )
        elif is_snapshot:
            image_data = request.data.get("image")
            if not image_data:
                return Response(
                    errors={"message": "no image data provided for snapshot."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            ai_response = process_stock_items(
                image_data, is_base64=True, company_id=company_id, branch_id=branch_id
            )
        else:
            return Response(
                errors={
                    "message": "invalid request, must specify is_upload or is_snapshot."
                },
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if "error" in ai_response:
            return Response(
                errors={"message": json.dumps(ai_response)},
                status_code=500,
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        return Response(data=ai_response, status_code=200, status=status.HTTP_200_OK)


class AIPriceListConverterAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def post(self, request, *args, **kwargs):
        company_id = request.data.get("company")
        is_upload = request.data.get("is_upload")
        is_snapshot = request.data.get("is_snapshot")

        if is_upload:
            image = request.FILES.get("image")
            if not image:
                return Response(
                    errors={"message": "no image provided for upload."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            ai_response = process_price_list(
                image, is_base64=False, company_id=company_id
            )
        elif is_snapshot:
            image_data = request.data.get("image")
            if not image_data:
                return Response(
                    errors={"message": "no image data provided for snapshot."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            ai_response = process_price_list(
                image_data, is_base64=True, company_id=company_id
            )
        else:
            return Response(
                errors={
                    "message": "invalid request, must specify is_upload or is_snapshot."
                },
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if "error" in ai_response:
            return Response(
                errors={"message": json.dumps(ai_response)},
                status_code=500,
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        return Response(data=ai_response, status_code=200, status=status.HTTP_200_OK)


class PriceListUploadView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = request.data
        company_id = data.get("company")
        price_list_data = data.get("price_list")

        # Fetch company and branch
        company = Company.objects.filter(id=company_id).first()

        if not company:
            return Response(
                {"error": "Invalid company or branch"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Process each item in the price list
        for item_data in price_list_data:
            category_name = item_data.get("category").strip().title()
            product_name = item_data.get("product").strip().title()
            price = item_data.get("price").replace(",", "")

            try:
                price = float(price)
            except ValueError:
                return Response(
                    {"error": "Invalid price format"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Handle Category (Case Insensitive)
            category = models.Category.objects.filter(
                company=company, name__iexact=category_name
            ).first()
            if not category:
                category = models.Category.objects.create(
                    company=company, name=category_name, created_by=request.user
                )

            # Handle Product (Case Insensitive)
            product = models.Product.objects.filter(
                company=company, name__iexact=product_name, category=category
            ).first()
            if not product:
                product = models.Product.objects.create(
                    company=company,
                    name=product_name,
                    category=category,
                    created_by=request.user,
                    selling_price=price,
                )
            else:
                # Update the product price if it already exists
                product.selling_price = price
                product.updated_by = request.user
                product.save()

            # Handle Price List
            price_list_entry, created = models.PriceList.objects.update_or_create(
                company=company,
                item=product,
                defaults={
                    "price": price,
                    "updated_by": request.user,
                    "created_by": request.user,
                    "category": category,
                },
            )

        return Response(
            {"success": "Price list updated successfully"}, status=status.HTTP_200_OK
        )


class ProductOrdersView(APIView):

    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    """
    API view to retrieve orders containing a specific product for a given company and branch.

    Methods:
        get(request, company_id, branch_id, product_id): Handles GET requests to return orders containing the product.
    """

    def get(self, request, company_id, branch_id, product_id):
        """
        Retrieves orders containing the specified product.

        Args:
            request: The HTTP request object.
            company_id: The ID of the company to filter orders.
            branch_id: The ID of the branch to filter orders.
            product_id: The ID of the product to filter orders.

        Returns:
            Response: A response object containing the orders data.
        """
        # Validate the company
        try:
            company = Company.objects.get(pk=company_id)
        except Company.DoesNotExist:
            return Response(
                {"error": f"Company with ID {company_id} does not exist."},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Validate the branch
        try:
            branch = models.Branch.objects.get(pk=branch_id, company=company)
        except models.Branch.DoesNotExist:
            return Response(
                {
                    "error": f"Branch with ID {branch_id} does not exist for company with ID {company_id}."
                },
                status=status.HTTP_404_NOT_FOUND,
            )

        # Get all OrderProduct instances with the specified product
        order_products = OrderProduct.objects.filter(product_id=product_id)

        # Get the orders related to those OrderProduct instances
        orders = Order.objects.filter(
            id__in=order_products.values("orders"), branch=branch
        )
        # Serialize the orders
        serialized_orders = ProductOrderSerializer(orders, many=True).data

        # Create the response data
        response_data = []
        count = len(serialized_orders)

        for order_data in serialized_orders:
            buyer = order_data.get("buyer")
            order_details = {
                "buyer_name": f"{buyer['first_name']} {buyer['last_name']}",
                "email": buyer["email"],
                "order_status": order_data["status"],
                "order_date": order_data["order_date"],
                "order_details": order_data,
            }
            response_data.append(order_details)

        return Response(
            {"orders_count": count, "orders": response_data}, status=status.HTTP_200_OK
        )


class AIStockSampleImageAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        file_path = os.path.join(settings.BASE_DIR, "media/images")
        return FileResponse(
            open(f"{file_path}/sample_stock_image.jpeg", "rb"), as_attachment=True
        )


class AIStockUploadAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def put(self, request, *args, **kwargs):
        serializer = serializers.AIStockUploadSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        models.StockDetail.ai_upload_stock(
            user=request.user, **serializer.validated_data
        )
        return Response(
            data={"message": "stock upload was successful."},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class CompanyProductCategoryAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        category_id = request.query_params.get("category")
        search = request.query_params.get("search")
        available_categories = (
            models.PriceList.objects.filter(company=company_id)
            .order_by("category__id")
            .distinct("category")
        )
        if search and is_valid_string(search):
            available_categories = available_categories.filter(
                category__name__icontains=search
            ).order_by("name")
        if category_id:
            if not is_valid_uuid(category_id):
                return Response(
                    errors={"message": "provide a valid category ID."},
                    status_code=400,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            available_categories = available_categories.filter(category=category_id)
        paginated_data = Paginator.paginate(
            request=request, queryset=available_categories
        )
        serializer = serializers.CompanyProductCategorySerializer(
            instance=paginated_data, many=True
        )
        data = {
            "message": "successfully fetched categories.",
            "categories": serializer.data,
            "count": len(serializer.data),
            "total_categories": available_categories.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)

    def post(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        category_id = request.query_params.get("category")

        subcategories = models.SubCategory.objects.filter(
            company_id=company_id, category_id=category_id
        )
        total_subcategories = subcategories.count()
        data = {
            "message": "successfully fetched subcategories.",
            "subcategories": subcategories.values("id", "name"),
            "count": total_subcategories,
            "total_subcategories": total_subcategories,
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class StockAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        serializer = serializers.StockSerializer(data=request.data, many=True)
        serializer.is_valid(raise_exception=True)
        stock_details = []
        for item in serializer.validated_data:
            product = item.get("item")
            quantity = item.pop("quantity", 0)
            stock_detail, created = models.StockDetail.objects.update_or_create(
                company=item["company"],
                branch=item["branch"],
                category=item["category"],
                item=item["item"],
                defaults={"uploaded_by": request.user, **item},
            )
            quantity_before = stock_detail.quantity
            stock_detail.quantity += quantity
            stock_detail.save()
            stock_detail.refresh_from_db()
            stock_details.append(stock_detail)
            # Register stock history.
            utils.register_stock_history(
                company=item["company"],
                branch=item["branch"],
                category=item["category"],
                item=item["item"],
                price=float(item["stock_price"]),
                quantity_before=quantity_before,
                quantity=quantity,
                quantity_after=stock_detail.quantity,
                transaction_type=stock_enums.StockHistoryChoices.STOCK_IN,
                status=stock_enums.StockHistoryStatusChoices.INCOMING,
                created_by=request.user,
            )
            if item.get("subcategory"):
                product.subcategory = item.get("subcategory")
            if item.get("vat") > 0:
                product.vat = item.get("vat")
            if item.get("sku"):
                product.sku = item.get("sku")
            product.save()
        serializer = serializers.StockSerializer(instance=stock_details, many=True)
        return Response(
            data={"message": "success", "payload": serializer.data},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class FinishedProductListCreateView(generics.ListCreateAPIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    queryset = models.FinishedProduct.objects.all()
    serializer_class = serializers.FinishedProductSerializer

    def get_queryset(self):
        queryset = models.FinishedProduct.objects.all()
        company_id = self.request.query_params.get("company_id", None)
        search_term = self.request.query_params.get("search", None)

        if company_id is not None:
            queryset = queryset.filter(company=company_id)

        if search_term is not None:
            queryset = queryset.filter(
                Q(name__icontains=search_term)
                | Q(recipes__product__name__icontains=search_term)
            ).distinct()

        return queryset


class FinishedProductRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    queryset = models.FinishedProduct.objects.all()
    serializer_class = serializers.FinishedProductSerializer

    def get_object(self):
        obj = super().get_object()
        if obj.company != self.request.user.company:
            raise ValidationError(
                detail={"error": "you cannot view product not in your company"},
                code=403,
            )
        return obj

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, "_prefetched_objects_cache", None):
            instance._prefetched_objects_cache = {}

        return Response(serializer.data)


class FinishedProductRecipeListCreateView(generics.ListCreateAPIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    queryset = models.FinishedProductRecipe.objects.all()
    serializer_class = serializers.FinishedProductRecipeSerializer


class FinishedProductRecipeRetrieveUpdateDestroyView(
    generics.RetrieveUpdateDestroyAPIView
):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [IsAuthenticated]
    queryset = models.FinishedProductRecipe.objects.all()
    serializer_class = serializers.FinishedProductRecipeSerializer

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, "_prefetched_objects_cache", None):
            instance._prefetched_objects_cache = {}

        return Response(serializer.data)


class ManageProductAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def patch(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        branch = request.query_params.get("branch")
        product_id = request.query_params.get("product")

        if product_id is None or not is_valid_uuid(product_id):
            return Response(
                errors={"message": "Provide a valid product ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        product = models.Product.objects.filter(id=product_id, company=company).last()
        if product is None:
            return Response(
                errors={"message": "Provide a valid product ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        if request.data == {}:
            return Response(
                errors={"message": "No field(s) was passed to be updated."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = serializers.ManageProductSerializer(
            data=request.data,
            context={
                "company": company,
                "product_id": product_id,
            },
        )
        serializer.is_valid(raise_exception=True)
        # product/item update.
        product_fields = [field.name for field in models.Product._meta.get_fields()]

        # price list update.
        price_list_fields = [
            field.name for field in models.PriceList._meta.get_fields()
        ]
        price_list = models.PriceList.objects.filter(
            company=company, item=product
        ).last()

        # stock details/inventory update.
        stock_detail_fields = [
            field.name for field in models.StockDetail._meta.get_fields()
        ]
        stock_detail = models.StockDetail.objects.filter(
            company=company, branch=branch, item=product
        ).last()

        for key, value in serializer.validated_data.items():
            if key in product_fields:
                setattr(product, key, value)
            if price_list is not None and key in price_list_fields:
                setattr(price_list, key, value)
            if stock_detail is not None and key in stock_detail_fields:
                setattr(stock_detail, key, value)
        product.save()
        if price_list is not None:
            price_list.save()
        if stock_detail is not None:
            stock_detail.save()

        return Response(
            data={"message": "product updated successfully."},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class DepleteStockAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
        CompanyOwnerPermission,
    ]

    def put(self, request, *args, **kwargs):
        ############################################################################
        #                       MONITOR USER API REQUESTS                          #
        #                                                                          #
        ############################################################################
        api_log = models.APILog.objects.create(
            user=request.user,
            method=request.method,
            path="/api/v1/stock/deplete/",
            request_headers=request.headers,
            request_body=request.data,
        )
        serializer = serializers.DepleteStockActionSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        deplete_response = utils.deplete_branch_quantity_available(
            company=serializer.validated_data["company"],
            branch=serializer.validated_data["branch"],
            category=serializer.validated_data["product"].category,
            item=serializer.validated_data["product"],
            quantity=serializer.validated_data["quantity"],
            on_hold=False,
        )
        if not isinstance(deplete_response, bool):
            utils.register_stock_history(
                company=serializer.validated_data["company"],
                branch=serializer.validated_data["branch"],
                category=serializer.validated_data["product"].category,
                item=serializer.validated_data["product"],
                price=serializer.validated_data["product"].selling_price,
                quantity_before=deplete_response.get("quantity_before"),
                quantity=serializer.validated_data["quantity"],
                quantity_after=deplete_response.get("quantity_after"),
                transaction_type=stock_enums.StockHistoryChoices.DEPLETE,
                status=stock_enums.StockHistoryStatusChoices.NEUTRAL,
                created_by=request.user,
                comment=serializer.validated_data.get("reason"),
            )
            item_deplete_history = {
                "company": serializer.validated_data.get("company").company_name,
                "company_id": str(serializer.validated_data.get("company").id),
                "branch": serializer.validated_data.get("branch").name,
                "branch_id": str(serializer.validated_data.get("branch").id),
                "category": serializer.validated_data.get("product").category.name,
                "category_id": str(
                    serializer.validated_data.get("product").category.id
                ),
                "product": serializer.validated_data.get("product").name,
                "product_id": str(serializer.validated_data.get("product").id),
                "deplete_quantity": deplete_response.get("deplete_quantity"),
                "quantity_before": deplete_response.get("quantity_before"),
                "quantity_after": deplete_response.get("quantity_after"),
                "deplete_message": deplete_response.get("message"),
            }
            items__depletion_history = {"item": item_deplete_history}
            api_log.response_body = str({"message": "product updated successfully."})
            api_log.items_depletion_history = items__depletion_history
            api_log.status_code = 200
            api_log.save()
        return Response(
            data={"message": "product updated successfully."},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class BranchSettingsAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
        # CompanyOwnerPermission,
    ]

    def put(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        serializer = serializers.BranchSettingsSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        branch = models.Branch.retrieve_company_branch(
            id=branch_id,
            company=company_id,
        )
        if branch is None:
            return Response(
                errors={"message": "provide a valid branch ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        branch.sell_without_inventory = serializer.validated_data.get(
            "sell_without_inventory", False
        )
        branch.transfer_charges_to_customer = serializer.validated_data.get(
            "transfer_charges_to_customer", False
        )
        branch.use_product_vat = serializer.validated_data.get("use_product_vat", False)
        branch.allow_store_credit = serializer.validated_data.get(
            "allow_store_credit", False
        )
        branch.save()
        return Response(
            data={"message": "branch was updated successfully"},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class PriceTagAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]
    serializer_class = serializers.PriceTagSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(created_by=request.user)
        return Response(
            data=serializer.data, status_code=201, status=status.HTTP_201_CREATED
        )

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        search = request.query_params.get("search")

        company_price_tags = models.PriceTag.objects.filter(
            Q(system_default=True) | Q(company=company)
        )
        if search and is_valid_string(search):
            company_price_tags = company_price_tags.filter(name__icontains=search)
        paginated_data = Paginator.paginate(
            request=request, queryset=company_price_tags
        )
        serializer = self.serializer_class(instance=paginated_data, many=True)
        data = {
            "price_tags": serializer.data,
            "count": len(serializer.data),
            "total": company_price_tags.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)

    def patch(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        price_tag = request.query_params.get("price_tag")

        if price_tag is None or not is_valid_uuid(price_tag):
            return Response(
                errors={"message": "provide a valid price tag ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        company_price_tag = models.PriceTag.objects.filter(
            company=company, id=price_tag
        ).first()
        if company_price_tag is None:
            return Response(
                errors={"message": "provide a valid price tag ID."},
                status_code=400,
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = serializers.UpdatePriceTagSerializer(
            company_price_tag, data=request.data, partial=True
        )
        serializer.is_valid(raise_exception=True)
        serializer.save(updated_by=request.user)
        return Response(
            data=serializer.data, status_code=200, status=status.HTTP_200_OK
        )

    def delete(self, request, *args, **kwargs):
        serializer = serializers.PriceTagDeleteSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        price_tag = serializer.validated_data["price_tag"]
        price_tag.delete()
        return Response(
            data={"status": True, "message": "price tag deleted successfully."},
            status_code=204,
            status=status.HTTP_204_NO_CONTENT,
        )


class PriceVariationAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        search = request.query_params.get("search")
        company_products = models.Product.objects.filter(company=company)
        if search and is_valid_string(search):
            company_products = company_products.filter(
                Q(category__name__icontains=search) | Q(name__icontains=search)
            )
        paginated_data = Paginator.paginate(request=request, queryset=company_products)
        serializer = serializers.ProductPriceVariationSerializer(
            instance=paginated_data, many=True
        )
        data = {
            "products": serializer.data,
            "count": len(serializer.data),
            "total": company_products.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)

    def put(self, request, *args, **kwargs):
        serializer = serializers.ManagePriceVariationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        for data in serializer.validated_data.get("products"):
            models.PriceVariation.manage_prices(
                user=request.user,
                company=serializer.validated_data.get("company"),
                product=data.get("product"),
                product_price=data.get("product_price"),
                selling_price=data.get("selling_price"),
                method=data.get("method"),
                price_variations=data.get("price_variations"),
            )
        return Response(
            data={"message": "successfully updated the price variation."},
            status_code=200,
            status=status.HTTP_200_OK,
        )


class GetCreateCategorySubCategory(APIView):
    permission_classes = [IpWhiteListPermission]

    def post(self, request):
        data = request.data
        company_id = data.get("company_id")
        brand_name = data.get("brand_name", "").title()
        category_name: str = data.get("category_name")
        subcategory_name = data.get("subcategory_name")

        try:
            company_instance = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(
                data={"message": "Invalid company ID."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        brand_instance, created = models.Brand.objects.get_or_create(
            name__iexact=brand_name
        )
        category_qs = models.Category.objects.filter(
            name=category_name.title(), company=company_instance
        )

        if category_qs.exists():
            category_instance = category_qs.first()

            subcategory_qs = models.SubCategory.objects.filter(
                name=subcategory_name.title(),
                category=category_instance,
                company=company_instance,
            )
            if subcategory_qs.exists():
                subcategory_instance = subcategory_qs.first()
            else:
                subcategory_instance = models.SubCategory.objects.create(
                    name=subcategory_name.title(),
                    category=category_instance,
                    company=company_instance,
                    created_by=company_instance.user,
                )
        else:
            category_instance = models.Category.objects.create(
                name=category_name.title(),
                company=company_instance,
                created_by=company_instance.user,
            )
            subcategory_instance = models.SubCategory.objects.create(
                name=subcategory_name.title(),
                category=category_instance,
                company=company_instance,
                created_by=company_instance.user,
            )

        return Response(
            data={
                "message": "ok",
                "category_id": category_instance.id,
                "sub_category_id": subcategory_instance.id,
                "brand_id": brand_instance.id,
            },
            status_code=200,
            status=status.HTTP_200_OK,
        )


class SupplierProductAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = serializers.SupplierProductSerializerIn(
            data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return APIResponse(
            {"message": "Product uploaded successfully", "data": response}
        )

    def put(self, request, pk):
        prod = models.SupplierProduct.objects.filter(
            supplier__email__iexact=request.user.email, id=pk
        )
        instance = prod.last() if prod else None
        if not instance:
            return APIResponse(
                {"message": "Product not found"}, status=status.HTTP_404_NOT_FOUND
            )
        serializer = serializers.SupplierProductSerializerIn(
            instance=instance, data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return APIResponse(
            {"message": "Product updated successfully", "data": response}
        )

    def get(self, request, pk=None):
        search = request.GET.get("search")
        category = request.GET.get("category_name")
        published = request.GET.get("publish")
        stock_out = request.GET.get("stock_out")
        date_from = request.GET.get("date_from")
        date_to = request.GET.get("date_to")

        true_false_check = ["true", "false"]

        query = Q(supplier__email__iexact=request.user.email)
        if pk:
            queryset = (
                models.SupplierProduct.objects.filter(query, id=pk).last() or None
            )
            if not queryset:
                return APIResponse(
                    {"message": "Product not found"}, status=status.HTTP_404_NOT_FOUND
                )
            return APIResponse(
                serializers.SupplierProductSerializerOut(
                    queryset, context={"request": request}
                ).data
            )
        if search:
            query &= Q(brand__name__icontains=search) | Q(name__icontains=search)
        if stock_out and stock_out in true_false_check:
            query &= Q(out_of_stock=eval(str(stock_out).capitalize()))
        if published and published in true_false_check:
            query &= Q(published=eval(str(published).capitalize()))
        if category:
            query &= Q(category__name__iexact=category)
        if date_from and date_to:
            query &= Q(created_at__range=[date_from, date_to])

        queryset = self.paginate_queryset(
            models.SupplierProduct.objects.filter(query)
            .order_by("-created_at")
            .distinct(),
            request,
        )
        serializer = serializers.SupplierProductSerializerOut(
            queryset, many=True, context={"request": request}
        ).data
        return APIResponse(
            {"message": "Success", "data": self.get_paginated_response(serializer).data}
        )


class BrandListAPIView(generics.ListAPIView):
    permission_classes = []
    serializer_class = serializers.BrandSerializerOut
    queryset = models.Brand.objects.all().order_by("-name")
    pagination_class = CustomPagination


class SubCategoryListAPIView(generics.ListAPIView):
    permission_classes = []
    serializer_class = serializers.SubcategorySerializer
    pagination_class = CustomPagination

    def get_queryset(self):
        request = self.request
        category_id = request.query_params.get("category_id")
        company_id = request.query_params.get("company_id")
        return models.SubCategory.objects.filter(
            category_id=category_id, company_id=company_id
        )


class UploadSupplierProductImageAPIView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [CustomUserAuthentication]

    def post(self, request):
        serializer = serializers.SupplierProductImageSerializerIn(
            data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return APIResponse({"message": "Product image uploaded", "data": response})

    def put(self, request, pk):
        product_image = models.SupplierProductImage.objects.filter(
            product__supplier__email__iexact=request.user.email, id=pk
        )
        instance = product_image.last() if product_image else None
        if not instance:
            return APIResponse(
                {"message": "Product Image not found"}, status=status.HTTP_404_NOT_FOUND
            )
        serializer = serializers.SupplierProductImageSerializerIn(
            instance=instance, data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return APIResponse(
            {"message": "Successfully set image as primary", "data": response}
        )

    def delete(self, request, pk):
        product_image = models.SupplierProductImage.objects.filter(
            product__supplier__email__iexact=request.user.email, id=pk
        )
        prod_image = product_image.last() if product_image else None
        if not prod_image:
            return APIResponse(
                {"message": "Product Image not found"}, status=status.HTTP_404_NOT_FOUND
            )

        prod_image.delete()
        return APIResponse({"message": "Image deleted"})


class BNPLSupplierProduct(APIView, CustomPagination):
    permission_classes = [IpWhiteListPermission]

    def post(self, request):
        serializer = serializers.CreateBNPLProductSupplierSerializerIn(
            data=request.data, context={"request": request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return APIResponse({"message": "Success", "data": response})

    def get(self, request, pk=None):
        # Fetch BNPL Product/Supplier
        param = request.GET.get("request_type")
        search = request.GET.get("search")
        date_from = request.GET.get("date_from")
        date_to = request.GET.get("date_to")
        if param == "product":
            if pk:
                try:
                    product = models.SupplierProduct.objects.get(id=pk)
                except models.SupplierProduct.DoesNotExist:
                    return APIResponse(
                        {"message": "Product not found"},
                        status=status.HTTP_404_NOT_FOUND,
                    )
                return APIResponse(
                    {
                        "message": "Success",
                        "data": serializers.SupplierProductSerializerOut(
                            product, context={"request": request}
                        ).data,
                    }
                )
            # query = Q(user=request)
            query = Q()
            if search:
                query &= Q(name__icontains=search) | Q(category__name__icontains=search)
            if date_to and date_from:
                query &= Q(created_at__range=[date_from, date_to])

            queryset = self.paginate_queryset(
                models.SupplierProduct.objects.filter(query)
                .order_by("-created_at")
                .distinct(),
                request,
            )
            serializer = serializers.SupplierProductSerializerOut(
                queryset, many=True, context={"request": request}
            ).data
            return APIResponse(
                {
                    "message": "Success",
                    "data": self.get_paginated_response(serializer).data,
                }
            )

        elif param == "supplier":
            if pk:
                try:
                    supplier = models.Supplier.objects.get(id=pk)
                except models.Supplier.DoesNotExist:
                    return APIResponse(
                        {"message": "Supplier not found"},
                        status=status.HTTP_404_NOT_FOUND,
                    )
                return APIResponse(
                    {
                        "message": "Success",
                        "data": SupplierSerializerOut(
                            supplier, context={"request": request}
                        ).data,
                    }
                )
            query = Q()
            if search:
                query &= (
                    Q(name__icontains=search)
                    | Q(email__iexact=search)
                    | Q(address__icontains=search)
                )
            if date_to and date_from:
                query &= Q(created_at__range=[date_from, date_to])

            queryset = self.paginate_queryset(
                models.Supplier.objects.filter(query)
                .order_by("-created_at")
                .distinct(),
                request,
            )
            serializer = SupplierSerializerOut(
                queryset, many=True, context={"request": request}
            ).data
            return APIResponse(
                {
                    "message": "Success",
                    "data": self.get_paginated_response(serializer).data,
                }
            )
        else:
            return APIResponse(
                {"message": "Invalid request type"}, status=status.HTTP_400_BAD_REQUEST
            )


class UnlistProductAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
        BranchRequiredPermission,
    ]

    def patch(self, request, *args, **kwargs):
        serializer = serializers.UnlistProductSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        branch = serializer.validated_data.get("branch")
        unlist = serializer.validated_data.get("unlist")
        products = models.Product.objects.filter(
            company=serializer.validated_data.get("company"),
            id__in=serializer.validated_data.get("items"),
        )
        if unlist:
            for item in products:
                item.selected_branches.remove(branch)
                item.save()
        else:
            for item in products:
                item.selected_branches.add(branch)
                item.save()
        return Response(
            data={"message": "successfully updated the selected items."},
            status_code=200,
            status=status.HTTP_200_OK,
        )

    def get(self, request, *args, **kwargs):
        company_id = request.query_params.get("company")
        branch_id = request.query_params.get("branch")
        search = request.query_params.get("search")
        # Branch is used for context.
        branch = models.Branch.retrieve_company_branch(id=branch_id, company=company_id)
        company_products = models.Product.objects.filter(company=company_id).exclude(
            selected_branches__id=branch_id
        )
        if search and is_valid_string(search):
            company_products = company_products.filter(
                Q(category__name__icontains=search) | Q(name__icontains=search)
            )
        paginated_response = Paginator.paginate(
            request=request, queryset=company_products
        )
        serializer = serializers.BranchProductSerializer(
            instance=paginated_response, many=True, context={"branch": branch}
        )
        data = {
            "message": "successfully fetched branch's products.",
            "products": serializer.data,
            "count": len(serializer.data),
            "total_products": company_products.count(),
        }
        return Response(data=data, status_code=200, status=status.HTTP_200_OK)


class CompanyBarcodeAPIView(APIView):
    authentication_classes = [CustomUserAuthentication]
    permission_classes = [
        IsAuthenticated,
        CompanyRequiredPermission,
    ]

    def patch(self, request, *args, **kwargs):
        serializer = serializers.CompanyBarcodeSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        company = serializer.validated_data.get("company")
        company.product_barcode = serializer.validated_data.get("product_barcode")
        company.save()
        return Response(
            data={"message": "successfully updated product barcode."},
            status_code=200,
            status=status.HTTP_200_OK,
        )

    def get(self, request, *args, **kwargs):
        company = request.query_params.get("company")
        company_barcode = Company.objects.filter(id=company).last()
        if company_barcode.product_barcode is None:
            unique_identifier = utils.company_barcode_identifier()
            company_barcode.product_barcode = f"{unique_identifier}000000"
            company_barcode.save()
            company_barcode.refresh_from_db()
        return Response(
            data={"barcode": company_barcode.product_barcode},
            status_code=200,
            status=status.HTTP_200_OK,
        )
