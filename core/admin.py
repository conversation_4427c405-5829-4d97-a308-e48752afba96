from django.contrib import admin
from django.contrib.admin.models import DELETION, LogEntry
from django.db.models.query import QuerySet
from django.urls import reverse
from django.utils.html import escape
from django.utils.safestring import mark_safe
from import_export.admin import ImportExportModelAdmin

from core import models, resources
from core.tasks import update_kyc_details

from .models import Guarantor


@admin.register(LogEntry)
class LogEntryAdmin(ImportExportModelAdmin):
    date_hierarchy = "action_time"

    list_filter = [
        "user",
        "content_type",
        "action_flag",
    ]

    search_fields = [
        "object_repr",
        "change_message",
    ]

    list_display = [
        "action_time",
        "user",
        "content_type",
        "object_link",
        "action_flag",
    ]

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def has_view_permission(self, request, obj=None):
        return request.user.is_superuser

    def object_link(self, obj):
        if obj.action_flag == DELETION:
            link = escape(obj.object_repr)
        else:
            ct = obj.content_type
            link = '<a href="%s">%s</a>' % (
                reverse(
                    "admin:%s_%s_change" % (ct.app_label, ct.model),
                    args=[obj.object_id],
                ),
                escape(obj.object_repr),
            )
        return mark_safe(link)

    object_link.admin_order_field = "object_repr"
    object_link.short_description = "object"


class UserResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.UserResource
    autocomplete_fields = [
        "default_company",
        "default_branch",
    ]
    search_fields = ["email"]
    list_filter = ["is_superuser", "is_staff"]
    list_display = (
        "id",
        "is_superuser",
        "is_staff",
        "liberty_pay_user_type",
        "default_company",
        "default_branch",
        "username",
        "email",
        "is_active",
        "liberty_pay_id",
        "phone_no",
        "first_name",
        "last_name",
        "bvn_number",
        "bvn_first_name",
        "bvn_last_name",
        "bvn_middle_name",
        "date_of_birth",
        "blacklist_status",
        "bvn_residentialAddress",
        "bvn_lgaOfResidence",
        "bvn_stateOfResidence",
        "state",
        "street",
        "lga",
        "nearest_landmark",
        "gender",
        "account_no",
        "account_name",
        "kyc_level",
        "bank",
        "bank_code",
        "liberty_pay_customer_status",
        "channel",
        "kyc_updated",
        "created_at",
        "updated_at",
    )

    def vfd_create_update_account(self, request, queryset: QuerySet[models.User]):

        message = "Invalid selection"
        for instance in queryset:
            message = update_kyc_details(user_id=instance.id)
        self.message_user(request, str(message))

    vfd_create_update_account.short_description = "VFD: create update account"
    vfd_create_update_account.allow_tags = True

    def black_list_email_from_funds_transfer(
        self, request, queryset: QuerySet[models.User]
    ):

        message = "Invalid selection"
        for instance in queryset:
            models.BlackListEntry.blacklist_user_email(user_email=instance.email)
            message = "success"

        self.message_user(request, str(message))

    black_list_email_from_funds_transfer.short_description = (
        "BlackList From FundsTransfer"
    )
    black_list_email_from_funds_transfer.allow_tags = True

    def general_black_list(self, request, queryset: QuerySet[models.User]):

        message = "Invalid selection"
        for instance in queryset:
            models.BlackListEntry.blacklist_user_email(
                user_email=instance.email, blacklist_type="GENERAL"
            )
            message = "success"
        self.message_user(request, str(message))

    general_black_list.short_description = "General BlackList"
    general_black_list.allow_tags = True

    def remove_from_fund_transfer_blacklist(
        self, request, queryset: QuerySet[models.User]
    ):

        message = "Invalid selection"
        for instance in queryset:

            models.BlackListEntry.remove_user_eamil_from_blacklist(
                user_email=instance.email
            )
            message = "success"
        self.message_user(request, str(message))

    remove_from_fund_transfer_blacklist.short_description = (
        "Remove From Funds Transfer BlackList"
    )
    remove_from_fund_transfer_blacklist.allow_tags = True

    def remove_from_general_black_list(self, request, queryset: QuerySet[models.User]):

        message = "Invalid selection"
        for instance in queryset:

            models.BlackListEntry.remove_user_eamil_from_blacklist(
                user_email=instance.email, blacklist_type="GENERAL"
            )
            message = "success"
        self.message_user(request, str(message))

    remove_from_general_black_list.short_description = "Remove From General BlackList"
    remove_from_general_black_list.allow_tags = True

    def remove_from_wallet_creation_black_list(
        self, request, queryset: QuerySet[models.User]
    ):

        message = "Invalid selection"
        for instance in queryset:

            models.BlackListEntry.remove_user_eamil_from_blacklist(
                user_email=instance.email, blacklist_type="ACCOUNT_CREATION"
            )  # remove user from send money black list

            message = "success"
        self.message_user(request, str(message))

    remove_from_wallet_creation_black_list.short_description = (
        "Remove From Wallet Creation BlackList"
    )
    remove_from_wallet_creation_black_list.allow_tags = True

    actions = [
        vfd_create_update_account,
        black_list_email_from_funds_transfer,
        general_black_list,
        remove_from_fund_transfer_blacklist,
        remove_from_general_black_list,
        remove_from_wallet_creation_black_list,
    ]


class ConstantTableResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.ConstantTableResource
    search_fields = []

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        return data


class KycDetailsResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.KycDetailsResource
    search_fields = [
        "user__email",
        "user_email",
        "bvn_number",
        "bvn_phone_number",
    ]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        return data


class IPWhitelistResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.IPWhitelistResource
    search_fields = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CategoryListResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CategoryListResource
    search_fields = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WaitListResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.WaitListResource
    search_fields = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BlackListEntryResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.BlackListEntryResource

    # search_fields = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OTPResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.OTPResource

    # search_fields = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class NotificationResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.NotificationResource

    search_fields = [
        "company__company_name",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PaystackPaymentResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.PaystackPaymentResource

    search_fields = [
        "payment_id",
        "reference",
    ]
    list_filter = [
        "status",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CampaignSenderIdResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CampaignSenderIdResource

    search_fields = [
        "sender_id",
        "sample_message",
        "company__company_name",
    ]
    list_filter = []

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CampaignBatchResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CampaignBatchResource
    search_fields = [
        "sender__sender_id",
        "message",
        "campaign_name",
        "company__company_name",
        "send_date",
    ]
    list_filter = [
        "priority_route",
        "flash_route",
        "send_later",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CampaignMessageResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CampaignMessageResource
    search_fields = [
        "sender__sender_id",
        "message",
        "phone_number",
        "campaign_name",
        "company__company_name",
        "send_date",
        "campaign_batch__id",
    ]
    list_filter = [
        "priority_route",
        "flash_route",
        "send_later",
        "status",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CampaignSmsTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CampaignSmsTransactionResource
    search_fields = [
        "company__company_name",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SalesTeamShiftResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.SalesTeamShiftResource
    search_fields = [
        "company__company_name",
        "branch__name",
    ]
    list_filter = ["created_at"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SalesTeamBreakShiftResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.SalesTeamBreakShiftResource
    search_fields = [
        "company__company_name",
        "branch__name",
    ]
    list_filter = ["created_at"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SalesUserRegistrationDumpResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.SalesUserRegistrationDumpResource
    search_fields = [
        "default_company__company_name",
        "default_branch__name",
    ]
    list_filter = ["created_at"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class IssueLogResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["user"]
    resource_class = resources.IssueLogResource
    search_fields = [
        "user__email",
    ]
    list_filter = ["date_created"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class VfdMeetingAdmin(ImportExportModelAdmin):
    resource_class = resources.VfdMeetingResource
    search_fields = [
        "work_email",
        "full_name",
        "phone_number",
        "country",
        "employee_headcount",
        "availability",
        "product_vertical",
        "created_at",
        "meeting_link",
        "paybox_agent_phone_number",
        "paybox_agent_name",
        "is_assigned",
    ]

    list_filter = ["created_at"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SalesAgentAdmin(ImportExportModelAdmin):
    resource_class = resources.SalesAgentResource
    search_fields = [
        "name",
        "email",
        "phone_number",
        "created_at",
    ]

    list_filter = ["created_at"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CampaignLeadResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.CampaignLeadResource
    list_filter = [
        "unique_id",
        "visited_page",
        "opened_video",
        "opened_form",
        "submitted_form_data",
    ]
    search_fields = ["unique_id"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OfflineApplicationAdmin(ImportExportModelAdmin):
    resource_class = resources.OfflineApplicationResource
    list_filter = [
        "app_type",
    ]
    search_fields = []
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UploadedMediaResourceAdmin(ImportExportModelAdmin):
    resource_class = resources.UploadedMediaResource
    list_filter = [
        "created_at",
    ]
    search_fields = []
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(models.User, UserResourceAdmin)
admin.site.register(models.ConstantTable, ConstantTableResourceAdmin)
admin.site.register(models.KycDetails, KycDetailsResourceAdmin)
admin.site.register(models.IPWhitelist, IPWhitelistResourceAdmin)
admin.site.register(models.CategoryList, CategoryListResourceAdmin)
admin.site.register(models.WaitList, WaitListResourceAdmin)
admin.site.register(models.BlackListEntry, BlackListEntryResourceAdmin)
admin.site.register(models.OTP, OTPResourceAdmin)
admin.site.register(models.Notification, NotificationResourceAdmin)
admin.site.register(models.PaystackPayment, PaystackPaymentResourceAdmin)
admin.site.register(models.CampaignSenderId, CampaignSenderIdResourceAdmin)
admin.site.register(models.CampaignBatch, CampaignBatchResourceAdmin)
admin.site.register(models.CampaignMessage, CampaignMessageResourceAdmin)
admin.site.register(models.CampaignSmsTransaction, CampaignSmsTransactionResourceAdmin)
admin.site.register(models.SalesTeamShift, SalesTeamShiftResourceAdmin)
admin.site.register(models.SalesTeamBreakShift, SalesTeamBreakShiftResourceAdmin)
admin.site.register(
    models.SalesUserRegistrationDump, SalesUserRegistrationDumpResourceAdmin
)
admin.site.register(models.IssueLog, IssueLogResourceAdmin)
admin.site.register(models.VfdMeeting, VfdMeetingAdmin)
admin.site.register(models.SalesAgents, SalesAgentAdmin)


admin.site.register(models.CampaignLead, CampaignLeadResourceAdmin)
admin.site.register(models.OfflineApplication, OfflineApplicationAdmin)
admin.site.register(models.UploadedMedia, UploadedMediaResourceAdmin)


class GuarantorAdmin(admin.ModelAdmin):
    list_filter = ["user", "email"]
    list_display = [
        "user",
        "email",
        "guarantor_name",
        "guarantor_phone_number",
        "guarantor_email",
        "guarantor_occupation",
        "guarantor_address",
        "next_of_kin_name",
        "next_of_kin_relationship",
        "next_of_kin_phone_number",
        "next_of_kin_address",
        "verified",
    ]


admin.site.register(Guarantor, GuarantorAdmin)
